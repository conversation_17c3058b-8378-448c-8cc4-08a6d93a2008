#!/usr/bin/env python3
"""
简单的房间管理系统测试脚本
"""

import os
import sys
import django
import asyncio

# 设置Django环境
sys.path.append('/home/<USER>/Desktop/program/project/Tuanzi')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import room_manager
from events.models import EventTemplate, EventStep
from channels.db import database_sync_to_async

User = get_user_model()

async def test_basic_room_operations():
    """测试基本房间操作"""
    print("🧪 开始测试房间管理系统...")
    
    try:
        # 1. 创建测试用户
        print("1. 创建测试用户...")

        @database_sync_to_async
        def create_test_users():
            host_user, created = User.objects.get_or_create(
                username='testhost',
                defaults={
                    'email': '<EMAIL>',
                    'subscription_level': User.SUBSCRIPTION_FREE
                }
            )
            if created:
                host_user.set_password('testpass123')
                host_user.save()

            participant_user, created = User.objects.get_or_create(
                username='testparticipant',
                defaults={
                    'email': '<EMAIL>',
                    'subscription_level': User.SUBSCRIPTION_FREE
                }
            )
            if created:
                participant_user.set_password('testpass123')
                participant_user.save()

            return host_user, participant_user

        host_user, participant_user = await create_test_users()
        
        print(f"   ✅ 用户创建完成: {host_user.username}, {participant_user.username}")
        
        # 2. 创建测试模板
        print("2. 创建测试模板...")

        @database_sync_to_async
        def create_test_template():
            template, created = EventTemplate.objects.get_or_create(
                name='测试模板',
                defaults={
                    'description': '用于测试的模板',
                    'creator': host_user
                }
            )

            # 添加测试步骤
            step, created = EventStep.objects.get_or_create(
                template=template,
                order=1,
                defaults={
                    'step_type': EventStep.STEP_FREE_CHAT,
                    'duration': 300
                }
            )
            return template

        template = await create_test_template()
        
        print(f"   ✅ 模板创建完成: {template.name}")
        
        # 3. 测试房间创建
        print("3. 测试房间创建...")
        room = await room_manager.create_room(
            host=host_user,
            template_id=template.id
        )
        
        print(f"   ✅ 房间创建成功: {room.room_code}")
        print(f"   - 房主: {room.host.username}")
        print(f"   - 状态: {room.status}")
        print(f"   - 最大人数: {room.max_participants}")
        
        # 4. 测试用户加入房间
        print("4. 测试用户加入房间...")
        success, message = await room_manager.join_room(room.room_code, participant_user)
        
        if success:
            print(f"   ✅ 用户加入成功: {message}")
        else:
            print(f"   ❌ 用户加入失败: {message}")
            return False
        
        # 5. 测试房间状态转换
        print("5. 测试房间状态转换...")
        success, message = await room_manager.transition_room_state(
            room.room_code, RoomState.IN_PROGRESS, host_user
        )
        
        if success:
            print(f"   ✅ 状态转换成功: {message}")
            # 重新获取房间状态
            room = await room_manager.get_room(room.room_code)
            print(f"   - 新状态: {room.status}")
        else:
            print(f"   ❌ 状态转换失败: {message}")
        
        # 6. 测试用户离开房间
        print("6. 测试用户离开房间...")
        success, message = await room_manager.leave_room(room.room_code, participant_user)
        
        if success:
            print(f"   ✅ 用户离开成功: {message}")
        else:
            print(f"   ❌ 用户离开失败: {message}")
        
        # 7. 测试房间关闭
        print("7. 测试房间关闭...")
        success, message = await room_manager.transition_room_state(
            room.room_code, RoomState.CLOSED, host_user
        )
        
        if success:
            print(f"   ✅ 房间关闭成功: {message}")
        else:
            print(f"   ❌ 房间关闭失败: {message}")
        
        print("\n🎉 所有测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sync_operations():
    """测试同步操作"""
    print("\n🧪 测试同步操作...")
    
    try:
        # 测试心跳机制
        room_code = "TEST01"
        room_manager.update_host_heartbeat(room_code)
        is_alive = room_manager.is_host_alive(room_code)
        print(f"   ✅ 心跳机制测试: {is_alive}")
        
        return True
    except Exception as e:
        print(f"   ❌ 同步操作测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 50)
    print("🚀 房间管理系统测试开始")
    print("=" * 50)
    
    # 运行异步测试
    async_result = await test_basic_room_operations()
    
    # 运行同步测试
    sync_result = test_sync_operations()
    
    print("\n" + "=" * 50)
    if async_result and sync_result:
        print("✅ 所有测试通过！房间管理系统工作正常。")
    else:
        print("❌ 部分测试失败，请检查系统配置。")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
