"""
数据库Bug修复测试用例

测试所有修复的数据库逻辑问题：
1. 房间自动关闭和销毁机制
2. 新用户组分配
3. 房间状态检查
4. 数据验证和约束
"""

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth.models import Group
from datetime import timedelta
from unittest.mock import patch

from core.models import User, Room, RoomParticipant
from events.models import EventTemplate, EventStep


class RoomLifecycleTest(TestCase):
    """测试房间生命周期管理"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.user
        )
    
    def test_room_expiration_check(self):
        """测试房间过期检查"""
        # 创建房间
        room = Room.objects.create(
            host=self.user,
            room_code='TEST01',
            event_template=self.template
        )
        
        # 设置限制（会自动设置过期时间）
        room.set_limits_by_subscription(self.user)
        
        # 验证过期时间已设置
        self.assertIsNotNone(room.expires_at)
        self.assertFalse(room.is_expired())
        
        # 模拟时间过去，房间过期
        past_time = timezone.now() - timedelta(hours=3)
        room.expires_at = past_time
        room.save()
        
        self.assertTrue(room.is_expired())
    
    def test_room_close_mechanism(self):
        """测试房间关闭机制"""
        room = Room.objects.create(
            host=self.user,
            room_code='TEST02',
            event_template=self.template
        )
        
        # 初始状态
        self.assertEqual(room.status, Room.STATUS_WAITING)
        self.assertIsNone(room.closed_at)
        
        # 关闭房间
        room.close_room()
        
        # 验证状态变更
        self.assertEqual(room.status, Room.STATUS_CLOSED)
        self.assertIsNotNone(room.closed_at)
    
    def test_room_destruction_check(self):
        """测试房间销毁检查"""
        room = Room.objects.create(
            host=self.user,
            room_code='TEST03',
            event_template=self.template
        )
        
        # 关闭房间
        room.close_room()
        
        # 刚关闭时不应该被销毁
        self.assertFalse(room.is_ready_for_destruction())
        
        # 模拟15分钟后
        past_time = timezone.now() - timedelta(minutes=16)
        room.closed_at = past_time
        room.save()
        
        self.assertTrue(room.is_ready_for_destruction())
    
    def test_room_can_join_logic(self):
        """测试房间加入逻辑"""
        room = Room.objects.create(
            host=self.user,
            room_code='TEST04',
            event_template=self.template,
            max_participants=2
        )
        
        # 设置过期时间
        room.expires_at = timezone.now() + timedelta(hours=2)
        room.save()
        
        # 正常情况下可以加入
        self.assertTrue(room.can_join())
        
        # 房间已满
        user2 = User.objects.create_user(username='user2', password='pass')
        user3 = User.objects.create_user(username='user3', password='pass')
        
        RoomParticipant.objects.create(room=room, user=self.user, role='host')
        RoomParticipant.objects.create(room=room, user=user2, role='participant')
        
        self.assertFalse(room.can_join())  # 已满
        
        # 房间过期
        room.expires_at = timezone.now() - timedelta(hours=1)
        room.save()
        
        # 移除一个参与者，但房间已过期
        RoomParticipant.objects.filter(user=user2).delete()
        self.assertFalse(room.can_join())  # 已过期
        
        # 房间关闭
        room.expires_at = timezone.now() + timedelta(hours=1)
        room.status = Room.STATUS_CLOSED
        room.save()
        
        self.assertFalse(room.can_join())  # 已关闭


class UserGroupAssignmentTest(TestCase):
    """测试用户组自动分配"""
    
    def test_new_user_gets_default_group(self):
        """测试新用户自动获得默认组"""
        # 确保默认组存在
        Group.objects.get_or_create(name='Regular Users')
        
        # 创建新用户
        user = User.objects.create_user(
            username='newuser',
            password='testpass123'
        )
        
        # 验证用户被分配到默认组
        self.assertTrue(user.groups.filter(name='Regular Users').exists())
        self.assertTrue(user.is_in_group('Regular Users'))
        self.assertEqual(user.get_user_role(), 'user')
    
    def test_superuser_gets_admin_group(self):
        """测试超级用户获得管理员组"""
        # 确保管理员组存在
        Group.objects.get_or_create(name='Super Administrators')
        
        # 创建超级用户
        user = User.objects.create_superuser(
            username='admin',
            password='adminpass123'
        )
        
        # 验证用户被分配到管理员组
        self.assertTrue(user.groups.filter(name='Super Administrators').exists())
        self.assertTrue(user.is_administrator())
        self.assertEqual(user.get_user_role(), 'super_admin')


class RoomParticipantConstraintTest(TestCase):
    """测试RoomParticipant约束和索引"""
    
    def setUp(self):
        """设置测试数据"""
        self.user1 = User.objects.create_user(username='user1', password='pass')
        self.user2 = User.objects.create_user(username='user2', password='pass')
        
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.user1
        )
        
        self.room = Room.objects.create(
            host=self.user1,
            room_code='TEST05',
            event_template=self.template
        )
    
    def test_unique_constraint(self):
        """测试唯一性约束"""
        # 创建第一个参与记录
        participant1 = RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_HOST
        )
        
        # 尝试创建重复记录应该失败
        with self.assertRaises(Exception):  # IntegrityError或类似错误
            RoomParticipant.objects.create(
                room=self.room,
                user=self.user1,
                role=RoomParticipant.ROLE_PARTICIPANT
            )
    
    def test_score_management(self):
        """测试得分管理"""
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.user2,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        # 测试得分操作
        self.assertEqual(participant.score, 0)
        
        participant.add_score(10)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 10)
        
        participant.add_score(5)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 15)
        
        participant.reset_score()
        participant.refresh_from_db()
        self.assertEqual(participant.score, 0)
    
    def test_role_permissions(self):
        """测试角色权限"""
        # 房主
        host = RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_HOST
        )
        
        self.assertTrue(host.can_control_game())
        self.assertTrue(host.can_manage_room())
        
        # 协管员
        moderator = RoomParticipant.objects.create(
            room=self.room,
            user=self.user2,
            role=RoomParticipant.ROLE_MODERATOR
        )
        
        self.assertFalse(moderator.can_control_game())
        self.assertTrue(moderator.can_manage_room())
        
        # 普通参与者
        participant = RoomParticipant.objects.create(
            room=Room.objects.create(
                host=self.user1,
                room_code='TEST06',
                event_template=self.template
            ),
            user=self.user2,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        self.assertFalse(participant.can_control_game())
        self.assertFalse(participant.can_manage_room())


class DataValidationTest(TestCase):
    """测试数据验证"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(username='testuser', password='pass')
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.user
        )
    
    def test_room_code_uniqueness(self):
        """测试房间代码唯一性"""
        room1 = Room.objects.create(
            host=self.user,
            room_code='UNIQUE1',
            event_template=self.template
        )
        
        # 尝试创建相同房间代码的房间应该失败
        with self.assertRaises(Exception):  # IntegrityError
            Room.objects.create(
                host=self.user,
                room_code='UNIQUE1',
                event_template=self.template
            )
    
    def test_subscription_limits(self):
        """测试订阅限制设置"""
        room = Room.objects.create(
            host=self.user,
            room_code='LIMITS1',
            event_template=self.template
        )
        
        # 测试免费版限制
        self.user.subscription_level = User.SUBSCRIPTION_FREE
        room.set_limits_by_subscription(self.user)
        
        self.assertEqual(room.max_participants, 10)
        self.assertEqual(room.duration_hours, 2)
        self.assertIsNotNone(room.expires_at)
        
        # 测试Pro版限制
        self.user.subscription_level = User.SUBSCRIPTION_PRO
        room.set_limits_by_subscription(self.user)
        
        self.assertEqual(room.max_participants, 500)
        self.assertEqual(room.duration_hours, 24)
        
        # 测试Max版限制
        self.user.subscription_level = User.SUBSCRIPTION_MAX
        room.set_limits_by_subscription(self.user)
        
        self.assertEqual(room.max_participants, 2000)
        self.assertEqual(room.duration_hours, 72)


if __name__ == '__main__':
    import unittest
    unittest.main()
