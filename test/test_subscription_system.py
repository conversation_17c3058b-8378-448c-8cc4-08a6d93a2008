#!/usr/bin/env python3
"""
测试订阅模式系统的脚本
验证用户模型、JWT载荷、房间限制等功能
"""

import os
import sys
import django
import json
from django.test import Client
from django.contrib.auth import get_user_model

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from core.models import User, Room
from events.models import EventTemplate, EventStep
from rest_framework_simplejwt.tokens import RefreshToken

def test_user_subscription_levels():
    """测试用户订阅等级功能"""
    print("=== 测试用户订阅等级 ===")
    
    # 创建不同订阅等级的用户
    free_user = User.objects.create_user(
        username='free_user',
        password='testpass123',
        subscription_level=User.SUBSCRIPTION_FREE
    )
    
    pro_user = User.objects.create_user(
        username='pro_user', 
        password='testpass123',
        subscription_level=User.SUBSCRIPTION_PRO
    )
    
    max_user = User.objects.create_user(
        username='max_user',
        password='testpass123', 
        subscription_level=User.SUBSCRIPTION_MAX
    )
    
    print(f"Free用户: {free_user}")
    print(f"Pro用户: {pro_user}")
    print(f"Max用户: {max_user}")
    
    return free_user, pro_user, max_user

def test_jwt_payload():
    """测试JWT载荷包含订阅等级"""
    print("\n=== 测试JWT载荷 ===")
    
    user = User.objects.get(username='free_user')
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    print(f"JWT载荷: {access_token.payload}")
    
    # 验证载荷包含subscription_level
    assert 'subscription_level' in access_token.payload
    assert access_token.payload['subscription_level'] == 'Free'
    print("✅ JWT载荷包含订阅等级信息")

def test_room_limits():
    """测试房间限制功能"""
    print("\n=== 测试房间限制 ===")
    
    # 创建测试模板
    free_user = User.objects.get(username='free_user')
    pro_user = User.objects.get(username='pro_user')
    
    template = EventTemplate.objects.create(
        name='测试模板',
        description='用于测试的模板',
        creator=free_user
    )
    
    # 添加基础步骤
    EventStep.objects.create(
        template=template,
        order=1,
        step_type=EventStep.STEP_GAME_PICTIONARY,
        duration=300
    )
    
    # 测试Free用户房间限制
    room_free = Room.objects.create(
        host=free_user,
        event_template=template,
        room_code='TEST01'
    )
    room_free.set_limits_by_subscription(free_user)
    room_free.save()
    
    print(f"Free用户房间限制: {room_free.max_participants}人, {room_free.duration_hours}小时")
    assert room_free.max_participants == 10
    assert room_free.duration_hours == 2
    
    # 测试Pro用户房间限制
    room_pro = Room.objects.create(
        host=pro_user,
        event_template=template,
        room_code='TEST02'
    )
    room_pro.set_limits_by_subscription(pro_user)
    room_pro.save()
    
    print(f"Pro用户房间限制: {room_pro.max_participants}人, {room_pro.duration_hours}小时")
    assert room_pro.max_participants == 500
    assert room_pro.duration_hours == 24
    
    print("✅ 房间限制功能正常")

def test_premium_step_restriction():
    """测试付费环节限制"""
    print("\n=== 测试付费环节限制 ===")
    
    free_user = User.objects.get(username='free_user')
    
    # 创建包含付费环节的模板
    premium_template = EventTemplate.objects.create(
        name='付费模板',
        description='包含付费环节的模板',
        creator=free_user
    )
    
    # 添加付费环节
    EventStep.objects.create(
        template=premium_template,
        order=1,
        step_type=EventStep.STEP_PAUSE,  # 付费环节
        duration=300
    )
    
    # 使用Django测试客户端测试API
    client = Client()
    
    # 获取JWT token
    response = client.post('/api/token/', {
        'username': 'free_user',
        'password': 'testpass123'
    })
    
    token_data = json.loads(response.content)
    token = token_data['access']
    
    # 尝试创建包含付费环节的房间
    response = client.post('/api/rooms/create/', 
        {'template_id': premium_template.id},
        HTTP_AUTHORIZATION=f'Bearer {token}',
        content_type='application/json'
    )
    
    print(f"API响应状态: {response.status_code}")
    print(f"API响应内容: {json.loads(response.content)}")
    
    # 应该返回403 Forbidden
    assert response.status_code == 403
    response_data = json.loads(response.content)
    assert 'upgrade_required' in response_data
    
    print("✅ 付费环节限制功能正常")

def cleanup():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    User.objects.filter(username__in=['free_user', 'pro_user', 'max_user']).delete()
    EventTemplate.objects.filter(name__in=['测试模板', '付费模板']).delete()
    Room.objects.filter(room_code__in=['TEST01', 'TEST02']).delete()
    print("✅ 测试数据已清理")

def main():
    """主测试函数"""
    try:
        print("开始测试订阅模式系统...")
        
        # 运行测试
        test_user_subscription_levels()
        test_jwt_payload()
        test_room_limits()
        test_premium_step_restriction()
        
        print("\n🎉 所有测试通过！订阅模式系统工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup()

if __name__ == '__main__':
    main()
