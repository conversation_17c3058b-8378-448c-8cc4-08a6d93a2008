"""
API tests for authentication endpoints.
"""

import pytest
import json
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


@pytest.mark.api
class TestAuthenticationAPI:
    """Test authentication-related API endpoints."""
    
    def setup_method(self):
        """Set up test data for each test method."""
        self.client = APIClient()
        self.test_user_data = {
            'username': 'api_test_user',
            'password': 'testpass123'
        }
    
    def teardown_method(self):
        """Clean up after each test method."""
        User.objects.filter(username='api_test_user').delete()
    
    def test_user_registration_success(self):
        """Test successful user registration."""
        response = self.client.post('/api/register/', self.test_user_data)

        assert response.status_code == status.HTTP_201_CREATED
        # RegisterView returns user data directly, not wrapped in 'user' field
        assert 'username' in response.data
        assert response.data['username'] == 'api_test_user'
        assert 'subscription_level' in response.data

        # Verify user was created in database
        user = User.objects.get(username='api_test_user')
        assert user.username == 'api_test_user'
        assert user.subscription_level == 'Free'  # Default level
    
    def test_user_registration_duplicate_username(self):
        """Test registration with duplicate username fails."""
        # Create first user
        User.objects.create_user(**self.test_user_data)
        
        # Try to create duplicate
        response = self.client.post('/api/register/', self.test_user_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_user_registration_missing_fields(self):
        """Test registration with missing required fields."""
        # Missing password
        response = self.client.post('/api/register/', {'username': 'test'})
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Missing username
        response = self.client.post('/api/register/', {'password': 'test123'})
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_jwt_token_obtain_success(self):
        """Test successful JWT token generation."""
        # Create user first
        user = User.objects.create_user(**self.test_user_data)
        
        # Get token
        response = self.client.post('/api/token/', self.test_user_data)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        assert 'refresh' in response.data
        
        # Verify token contains subscription level
        from jwt import decode as jwt_decode
        token = response.data['access']
        decoded = jwt_decode(token, options={"verify_signature": False})
        assert 'subscription_level' in decoded
        assert decoded['subscription_level'] == 'Free'
    
    def test_jwt_token_obtain_invalid_credentials(self):
        """Test JWT token generation with invalid credentials."""
        # Create user first
        User.objects.create_user(**self.test_user_data)
        
        # Try with wrong password
        wrong_data = self.test_user_data.copy()
        wrong_data['password'] = 'wrongpassword'
        
        response = self.client.post('/api/token/', wrong_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_jwt_token_obtain_nonexistent_user(self):
        """Test JWT token generation for non-existent user."""
        response = self.client.post('/api/token/', {
            'username': 'nonexistent',
            'password': 'password123'
        })
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_jwt_token_refresh_success(self):
        """Test successful JWT token refresh."""
        # Create user and get initial tokens
        user = User.objects.create_user(**self.test_user_data)
        refresh = RefreshToken.for_user(user)
        
        # Refresh token
        response = self.client.post('/api/token/refresh/', {
            'refresh': str(refresh)
        })
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
    
    def test_jwt_token_refresh_invalid_token(self):
        """Test JWT token refresh with invalid token."""
        response = self.client.post('/api/token/refresh/', {
            'refresh': 'invalid_token'
        })
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without authentication."""
        response = self.client.get('/api/rooms/create/')
        # Should return 401 (Unauthorized) or 403 (Forbidden)
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
    
    def test_protected_endpoint_with_valid_token(self):
        """Test accessing protected endpoint with valid token."""
        # Create user and get token
        user = User.objects.create_user(**self.test_user_data)
        refresh = RefreshToken.for_user(user)
        
        # Set authorization header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # Access protected endpoint (should not return 401)
        response = self.client.get('/api/rooms/create/')
        # Note: This might return 405 (Method Not Allowed) or other status,
        # but should not return 401 (Unauthorized)
        assert response.status_code != status.HTTP_401_UNAUTHORIZED
    
    def test_protected_endpoint_with_invalid_token(self):
        """Test accessing protected endpoint with invalid token."""
        # Set invalid authorization header
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')

        response = self.client.get('/api/rooms/create/')
        # Should return 401 (Unauthorized) or 403 (Forbidden)
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
    
    def test_subscription_level_in_jwt_payload(self):
        """Test that JWT payload contains correct subscription level."""
        # Test different subscription levels
        subscription_levels = ['Free', 'Pro', 'Max']
        
        for level in subscription_levels:
            # Create user with specific subscription level
            user_data = self.test_user_data.copy()
            user_data['username'] = f'user_{level.lower()}'
            
            user = User.objects.create_user(
                username=user_data['username'],
                password=user_data['password'],
                subscription_level=level
            )
            
            # Get token
            response = self.client.post('/api/token/', user_data)
            assert response.status_code == status.HTTP_200_OK
            
            # Verify subscription level in token
            from jwt import decode as jwt_decode
            token = response.data['access']
            decoded = jwt_decode(token, options={"verify_signature": False})
            assert decoded['subscription_level'] == level
            
            # Cleanup
            user.delete()
