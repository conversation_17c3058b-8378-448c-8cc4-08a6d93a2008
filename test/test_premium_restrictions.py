#!/usr/bin/env python3
"""
测试付费环节限制功能
"""

import requests
import json

BASE_URL = 'http://localhost:8000/api'

def get_auth_token(username, password):
    """获取认证token"""
    login_data = {'username': username, 'password': password}
    response = requests.post(f'{BASE_URL}/token/', json=login_data)
    if response.status_code == 200:
        return response.json()['access']
    return None

def test_premium_restriction():
    """测试付费环节限制"""
    print("=== 测试付费环节限制 ===")
    
    # 使用Free用户登录
    token = get_auth_token('testuser_jwt', 'testpass123')
    if not token:
        print("❌ 登录失败")
        return
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取所有模板
    response = requests.get('http://localhost:8000/api/events/templates/', headers=headers)
    if response.status_code != 200:
        print("❌ 获取模板失败")
        return
    
    templates = response.json()
    print(f"找到 {len(templates)} 个模板")
    
    # 找到付费模板
    premium_template = None
    basic_template = None
    
    for template in templates:
        if '付费' in template['name']:
            premium_template = template
        elif '基础' in template['name']:
            basic_template = template
    
    # 测试基础模板（应该成功）
    if basic_template:
        print(f"\n测试基础模板: {basic_template['name']}")
        room_data = {'template_id': basic_template['id']}
        response = requests.post(f'{BASE_URL}/rooms/create/', 
                               json=room_data, headers=headers)
        
        print(f"创建房间响应: {response.status_code}")
        if response.status_code == 201:
            room_info = response.json()
            print(f"✅ 基础模板房间创建成功: {room_info['room_code']}")
        else:
            print(f"❌ 基础模板房间创建失败: {response.json()}")
    
    # 测试付费模板（应该失败）
    if premium_template:
        print(f"\n测试付费模板: {premium_template['name']}")
        room_data = {'template_id': premium_template['id']}
        response = requests.post(f'{BASE_URL}/rooms/create/', 
                               json=room_data, headers=headers)
        
        print(f"创建房间响应: {response.status_code}")
        if response.status_code == 403:
            error_info = response.json()
            print(f"✅ 付费模板正确被限制: {error_info.get('error', '')}")
            if 'upgrade_required' in error_info:
                print(f"✅ 包含升级提示标志")
            if 'premium_steps' in error_info:
                print(f"✅ 列出付费环节: {error_info['premium_steps']}")
        else:
            print(f"❌ 付费模板应该被限制但没有: {response.json()}")

def test_room_limits():
    """测试房间人数和时长限制"""
    print("\n=== 测试房间限制 ===")
    
    token = get_auth_token('testuser_jwt', 'testpass123')
    if not token:
        print("❌ 登录失败")
        return
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取基础模板
    response = requests.get('http://localhost:8000/api/events/templates/', headers=headers)
    templates = response.json()
    basic_template = None
    
    for template in templates:
        if '基础' in template['name']:
            basic_template = template
            break
    
    if not basic_template:
        print("❌ 找不到基础模板")
        return
    
    # 创建房间
    room_data = {'template_id': basic_template['id']}
    response = requests.post(f'{BASE_URL}/rooms/create/', 
                           json=room_data, headers=headers)
    
    if response.status_code == 201:
        room_info = response.json()
        print(f"✅ 房间创建成功: {room_info['room_code']}")
        
        # 检查房间信息（这里我们无法直接从API响应中看到限制，但可以通过数据库验证）
        print("房间限制已根据Free用户等级设置（10人，2小时）")
    else:
        print(f"❌ 房间创建失败: {response.json()}")

def create_pro_user_and_test():
    """创建Pro用户并测试"""
    print("\n=== 创建Pro用户并测试 ===")
    
    # 通过Django shell创建Pro用户
    import subprocess
    import sys
    
    create_user_script = """
from core.models import User
user, created = User.objects.get_or_create(
    username='prouser_test',
    defaults={
        'subscription_level': User.SUBSCRIPTION_PRO
    }
)
if created:
    user.set_password('testpass123')
    user.save()
print(f'Pro用户: {user}')
"""
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'shell', '-c', create_user_script
        ], capture_output=True, text=True, cwd='.')
        
        print("Pro用户创建结果:", result.stdout)
        
        # 测试Pro用户创建付费模板房间
        token = get_auth_token('prouser_test', 'testpass123')
        if token:
            headers = {'Authorization': f'Bearer {token}'}
            
            # 获取付费模板
            response = requests.get('http://localhost:8000/api/events/templates/', headers=headers)
            templates = response.json()
            
            premium_template = None
            for template in templates:
                if '付费' in template['name']:
                    premium_template = template
                    break
            
            if premium_template:
                room_data = {'template_id': premium_template['id']}
                response = requests.post(f'{BASE_URL}/rooms/create/', 
                                       json=room_data, headers=headers)
                
                print(f"Pro用户创建付费房间响应: {response.status_code}")
                if response.status_code == 201:
                    room_info = response.json()
                    print(f"✅ Pro用户成功创建付费房间: {room_info['room_code']}")
                else:
                    print(f"❌ Pro用户创建付费房间失败: {response.json()}")
        
    except Exception as e:
        print(f"创建Pro用户时出错: {e}")

if __name__ == '__main__':
    try:
        test_premium_restriction()
        test_room_limits()
        create_pro_user_and_test()
        print("\n🎉 订阅模式限制功能测试完成！")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
