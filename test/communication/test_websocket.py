"""
Tests for WebSocket communication and real-time features.
"""

import pytest
import json
import asyncio
from django.contrib.auth import get_user_model
from channels.testing import Websocket<PERSON>ommunicator
from channels.db import database_sync_to_async
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import Room, RoomParticipant, RoomState, UserState
from core.consumers import RoomConsumer
from core.services.room_manager import room_manager
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.communication
class TestWebSocketCommunication:
    """Test WebSocket communication functionality."""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self):
        """Set up test data for each test method."""
        # Create test users
        self.host_user = await database_sync_to_async(User.objects.create_user)(
            username='ws_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        self.participant_user = await database_sync_to_async(User.objects.create_user)(
            username='ws_participant',
            password='testpass123',
            subscription_level='Free'
        )
        
        # Create test template
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='WebSocket Test Template',
                description='Template for WebSocket testing',
                creator=self.host_user
            )
            
            EventStep.objects.create(
                template=template,
                order=1,
                step_type=EventStep.STEP_GAME_PICTIONARY,
                duration=300,
                name='Pictionary Round'
            )
            
            EventStep.objects.create(
                template=template,
                order=2,
                step_type=EventStep.STEP_FREE_CHAT,
                duration=180,
                name='Free Chat'
            )
            
            return template
        
        self.template = await create_template()
        
        # Create test room
        self.room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        # Add participant to room
        await room_manager.join_room(self.room.room_code, self.participant_user)
    
    async def teardown_method(self):
        """Clean up after each test method."""
        await database_sync_to_async(Room.objects.filter(room_code__startswith='WS').delete)()
        await database_sync_to_async(EventTemplate.objects.filter(name__contains='WebSocket Test').delete)()
        await database_sync_to_async(User.objects.filter(username__startswith='ws_').delete)()
    
    async def get_authenticated_communicator(self, user, room_code):
        """Get an authenticated WebSocket communicator."""
        # Generate JWT token for user
        refresh = RefreshToken.for_user(user)
        token = str(refresh.access_token)
        
        # Create communicator with token
        communicator = WebsocketCommunicator(
            RoomConsumer.as_asgi(),
            f"/ws/room/{room_code}/?token={token}"
        )
        
        return communicator
    
    async def test_websocket_connection_success(self):
        """Test successful WebSocket connection."""
        communicator = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        
        connected, subprotocol = await communicator.connect()
        assert connected
        
        # Should receive room state message
        response = await communicator.receive_json_from()
        assert response['type'] == 'room.state'
        assert response['payload']['room_code'] == self.room.room_code
        
        await communicator.disconnect()
    
    async def test_websocket_connection_invalid_token(self):
        """Test WebSocket connection with invalid token."""
        communicator = WebsocketCommunicator(
            RoomConsumer.as_asgi(),
            f"/ws/room/{self.room.room_code}/?token=invalid_token"
        )
        
        connected, subprotocol = await communicator.connect()
        assert not connected
    
    async def test_websocket_connection_invalid_room(self):
        """Test WebSocket connection to non-existent room."""
        communicator = await self.get_authenticated_communicator(
            self.host_user, "INVALID"
        )
        
        connected, subprotocol = await communicator.connect()
        # Connection might succeed but should receive error message
        if connected:
            response = await communicator.receive_json_from()
            assert response['type'] == 'error'
            await communicator.disconnect()
    
    async def test_chat_message_broadcast(self):
        """Test chat message broadcasting."""
        # Connect both users
        host_comm = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        participant_comm = await self.get_authenticated_communicator(
            self.participant_user, self.room.room_code
        )
        
        await host_comm.connect()
        await participant_comm.connect()
        
        # Clear initial messages
        await host_comm.receive_json_from()  # room state
        await participant_comm.receive_json_from()  # room state
        
        # Send chat message from host
        await host_comm.send_json_to({
            'action': 'chat_message',
            'payload': {'message': 'Hello everyone!'}
        })
        
        # Both users should receive the message
        host_response = await host_comm.receive_json_from()
        participant_response = await participant_comm.receive_json_from()
        
        assert host_response['type'] == 'chat.message'
        assert host_response['payload']['message'] == 'Hello everyone!'
        assert host_response['payload']['sender'] == 'ws_host'
        
        assert participant_response['type'] == 'chat.message'
        assert participant_response['payload']['message'] == 'Hello everyone!'
        assert participant_response['payload']['sender'] == 'ws_host'
        
        await host_comm.disconnect()
        await participant_comm.disconnect()
    
    async def test_room_state_transition_broadcast(self):
        """Test that room state transitions are broadcasted."""
        # Connect both users
        host_comm = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        participant_comm = await self.get_authenticated_communicator(
            self.participant_user, self.room.room_code
        )
        
        await host_comm.connect()
        await participant_comm.connect()
        
        # Clear initial messages
        await host_comm.receive_json_from()
        await participant_comm.receive_json_from()
        
        # Host starts the game
        await host_comm.send_json_to({
            'action': 'start_game',
            'payload': {}
        })
        
        # Both users should receive state change notification
        host_response = await host_comm.receive_json_from()
        participant_response = await participant_comm.receive_json_from()
        
        assert host_response['type'] == 'room.state'
        assert host_response['payload']['status'] == RoomState.IN_PROGRESS
        
        assert participant_response['type'] == 'room.state'
        assert participant_response['payload']['status'] == RoomState.IN_PROGRESS
        
        await host_comm.disconnect()
        await participant_comm.disconnect()
    
    async def test_next_step_action(self):
        """Test next step action in WebSocket."""
        # Start the game first
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        host_comm = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        
        await host_comm.connect()
        await host_comm.receive_json_from()  # Clear initial message
        
        # Send next step action
        await host_comm.send_json_to({
            'action': 'next_step',
            'payload': {}
        })
        
        # Should receive step change notification
        response = await host_comm.receive_json_from()
        assert response['type'] in ['step.started', 'game.pictionary.started']
        
        await host_comm.disconnect()
    
    async def test_drawing_data_broadcast(self):
        """Test drawing data broadcasting for Pictionary."""
        # Start the game and move to Pictionary step
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        host_comm = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        participant_comm = await self.get_authenticated_communicator(
            self.participant_user, self.room.room_code
        )
        
        await host_comm.connect()
        await participant_comm.connect()
        
        # Clear initial messages
        await host_comm.receive_json_from()
        await participant_comm.receive_json_from()
        
        # Send drawing data
        drawing_data = {
            'type': 'stroke',
            'points': [{'x': 100, 'y': 100}, {'x': 150, 'y': 150}],
            'color': '#000000',
            'width': 5
        }
        
        await host_comm.send_json_to({
            'action': 'send_drawing',
            'payload': drawing_data
        })
        
        # Participant should receive drawing data (but not host - no echo)
        participant_response = await participant_comm.receive_json_from()
        assert participant_response['type'] == 'drawing.data'
        assert participant_response['payload']['type'] == 'stroke'
        
        await host_comm.disconnect()
        await participant_comm.disconnect()
    
    async def test_user_join_notification(self):
        """Test that user join events are broadcasted."""
        # Connect host first
        host_comm = await self.get_authenticated_communicator(
            self.host_user, self.room.room_code
        )
        await host_comm.connect()
        await host_comm.receive_json_from()  # Clear initial message
        
        # Create new user and join room
        new_user = await database_sync_to_async(User.objects.create_user)(
            username='ws_new_user',
            password='testpass123'
        )
        
        await room_manager.join_room(self.room.room_code, new_user)
        
        # Host should receive user join notification
        response = await host_comm.receive_json_from()
        assert response['type'] == 'user.joined'
        assert response['payload']['username'] == 'ws_new_user'
        
        await host_comm.disconnect()
        
        # Cleanup
        await database_sync_to_async(new_user.delete)()
    
    async def test_permission_validation(self):
        """Test that WebSocket actions validate permissions."""
        # Connect participant
        participant_comm = await self.get_authenticated_communicator(
            self.participant_user, self.room.room_code
        )
        
        await participant_comm.connect()
        await participant_comm.receive_json_from()  # Clear initial message
        
        # Participant tries to start game (should fail)
        await participant_comm.send_json_to({
            'action': 'start_game',
            'payload': {}
        })
        
        # Should receive error message
        response = await participant_comm.receive_json_from()
        assert response['type'] == 'error'
        assert 'permission' in response['payload']['message'].lower()
        
        await participant_comm.disconnect()
