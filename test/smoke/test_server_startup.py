"""
Smoke tests to ensure the server can start up and basic services are available.
"""

import pytest
import os
import django
from django.test import TestCase
from django.core.management import execute_from_command_line
from django.db import connection
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from core.models import Room, RoomParticipant, RoomState, UserState
from events.models import EventTemplate, EventStep
from games.models import Game, PictionaryGame

User = get_user_model()


@pytest.mark.smoke
class TestServerStartup:
    """Test basic server startup and configuration."""
    
    def test_django_settings_loaded(self):
        """Test that Django settings are properly loaded."""
        from django.conf import settings
        assert settings.configured
        # DEBUG might be False in test environment, which is fine
        assert hasattr(settings, 'DEBUG')
        assert 'core' in settings.INSTALLED_APPS
        assert 'events' in settings.INSTALLED_APPS
        assert 'games' in settings.INSTALLED_APPS
        assert 'channels' in settings.INSTALLED_APPS
    
    def test_database_connection(self):
        """Test that database connection is working."""
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
    
    def test_models_can_be_imported(self):
        """Test that all core models can be imported."""
        # Core models
        assert Room is not None
        assert RoomParticipant is not None
        assert User is not None
        
        # Event models
        assert EventTemplate is not None
        assert EventStep is not None
        
        # Game models
        assert Game is not None
        assert PictionaryGame is not None
    
    def test_enums_are_defined(self):
        """Test that state enums are properly defined."""
        # Room states
        assert hasattr(RoomState, 'WAITING')
        assert hasattr(RoomState, 'ACTIVE')
        assert hasattr(RoomState, 'REVIEW')
        assert hasattr(RoomState, 'CLOSED')
        
        # User states
        assert hasattr(UserState, 'JOINED')
        assert hasattr(UserState, 'READY')
        assert hasattr(UserState, 'PLAYING')
        assert hasattr(UserState, 'SPECTATING')
    
    def test_database_tables_exist(self):
        """Test that all required database tables exist."""
        with connection.cursor() as cursor:
            # Get all table names
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            # Check core tables
            assert 'core_user' in tables
            assert 'core_room' in tables
            assert 'core_roomparticipant' in tables
            
            # Check event tables
            assert 'events_eventtemplate' in tables
            assert 'events_eventstep' in tables
            
            # Check game tables
            assert 'games_game' in tables
            assert 'games_pictionarygame' in tables
    
    def test_user_model_fields(self):
        """Test that User model has required fields."""
        user_fields = [f.name for f in User._meta.fields]
        
        required_fields = [
            'username', 'email', 'password', 'subscription_level',
            'is_active', 'is_staff', 'date_joined'
        ]
        
        for field in required_fields:
            assert field in user_fields, f"User model missing field: {field}"
    
    def test_room_model_fields(self):
        """Test that Room model has required fields."""
        room_fields = [f.name for f in Room._meta.fields]
        
        required_fields = [
            'room_code', 'host', 'status', 'created_at', 'expires_at',
            'closed_at', 'review_started_at', 'last_activity_at',
            'event_template', 'current_step_order', 'max_participants'
        ]
        
        for field in required_fields:
            assert field in room_fields, f"Room model missing field: {field}"
    
    def test_room_participant_model_fields(self):
        """Test that RoomParticipant model has required fields."""
        participant_fields = [f.name for f in RoomParticipant._meta.fields]
        
        required_fields = [
            'room', 'user', 'joined_at', 'last_active_at', 'role',
            'score', 'state', 'left_at'
        ]
        
        for field in required_fields:
            assert field in participant_fields, f"RoomParticipant model missing field: {field}"


@pytest.mark.smoke
def test_can_create_basic_objects():
    """Test that we can create basic model instances."""
    # Create a user
    user = User.objects.create_user(
        username='smoke_test_user',
        password='testpass123',
        subscription_level='Free'
    )
    assert user.id is not None
    assert user.username == 'smoke_test_user'
    
    # Create a template
    template = EventTemplate.objects.create(
        name='Smoke Test Template',
        description='A template for smoke testing',
        creator=user
    )
    assert template.id is not None
    
    # Create a room
    room = Room.objects.create(
        room_code='SMOKE01',
        host=user,
        event_template=template,
        status=RoomState.OPEN
    )
    assert room.id is not None
    assert room.room_code == 'SMOKE01'
    
    # Cleanup
    room.delete()
    template.delete()
    user.delete()


@pytest.mark.smoke
def test_subscription_levels():
    """Test that subscription levels are working correctly."""
    # Test Free user
    free_user = User.objects.create_user(
        username='free_smoke_user',
        password='testpass123',
        subscription_level='Free'
    )
    assert free_user.subscription_level == 'Free'
    
    # Test Pro user
    pro_user = User.objects.create_user(
        username='pro_smoke_user',
        password='testpass123',
        subscription_level='Pro'
    )
    assert pro_user.subscription_level == 'Pro'
    
    # Test Max user
    max_user = User.objects.create_user(
        username='max_smoke_user',
        password='testpass123',
        subscription_level='Max'
    )
    assert max_user.subscription_level == 'Max'
    
    # Cleanup
    free_user.delete()
    pro_user.delete()
    max_user.delete()
