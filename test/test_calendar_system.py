"""
日历和预订系统测试集
测试日历数据API、预约房间功能、状态机转换等核心功能
"""

import pytest
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import Room, RoomParticipant, RoomState, UserState
from events.models import EventTemplate, EventStep

User = get_user_model()


class CalendarSystemTestCase(APITestCase):
    """日历系统测试用例"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='testuser1',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        self.user2 = User.objects.create_user(
            username='testuser2', 
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.user1
        )
        
        # 创建模板步骤
        EventStep.objects.create(
            template=self.template,
            name='测试步骤',
            step_type='FREE_CHAT',
            order=1,
            duration=300
        )
        
        # 设置API客户端
        self.client = APIClient()
        
    def get_auth_token(self, user):
        """获取用户认证token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def authenticate_user(self, user):
        """认证用户"""
        token = self.get_auth_token(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        return token


class TestCalendarDataAPI(CalendarSystemTestCase):
    """测试日历数据API"""
    
    def test_get_calendar_data_authenticated(self):
        """测试认证用户获取日历数据"""
        self.authenticate_user(self.user1)
        
        # 创建一些预约房间
        future_time = timezone.now() + timedelta(hours=2)
        room1 = Room.objects.create(
            room_code='TEST01',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=future_time,
            duration_hours=2
        )
        
        url = reverse('calendar-data')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('reservations', data)
        self.assertIn('total_count', data)
        self.assertEqual(data['total_count'], 1)
        
        reservation = data['reservations'][0]
        self.assertEqual(reservation['room_code'], 'TEST01')
        self.assertEqual(reservation['host_username'], 'testuser1')
        self.assertEqual(reservation['status'], 'SCHEDULED')
    
    def test_get_calendar_data_unauthenticated(self):
        """测试未认证用户访问日历数据"""
        url = reverse('calendar-data')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_calendar_data_with_date_filter(self):
        """测试日历数据的日期过滤"""
        self.authenticate_user(self.user1)
        
        # 创建不同时间的预约
        today = timezone.now().date()
        tomorrow = today + timedelta(days=1)
        
        # 今天的预约
        room_today = Room.objects.create(
            room_code='TODAY1',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.make_aware(
                datetime.combine(today, datetime.min.time()) + timedelta(hours=14)
            ),
            duration_hours=1
        )
        
        # 明天的预约
        room_tomorrow = Room.objects.create(
            room_code='TOMOR1',
            host=self.user2,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.make_aware(
                datetime.combine(tomorrow, datetime.min.time()) + timedelta(hours=10)
            ),
            duration_hours=2
        )
        
        url = reverse('calendar-data')
        
        # 测试获取今天的预约
        response = self.client.get(url, {'date': today.isoformat()})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertGreaterEqual(data['total_count'], 1)
        
        # 验证返回的预约包含今天的
        room_codes = [r['room_code'] for r in data['reservations']]
        self.assertIn('TODAY1', room_codes)


class TestScheduleRoomAPI(CalendarSystemTestCase):
    """测试预约房间API"""
    
    def test_schedule_room_success(self):
        """测试成功预约房间"""
        self.authenticate_user(self.user1)
        
        future_time = timezone.now() + timedelta(hours=3)
        schedule_data = {
            'name': '测试预约房间',
            'template_id': self.template.id,
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        
        self.assertIn('room_code', data)
        self.assertEqual(data['status'], 'SCHEDULED')
        self.assertIn('scheduled_start_time', data)
        
        # 验证房间确实被创建
        room = Room.objects.get(room_code=data['room_code'])
        self.assertEqual(room.host, self.user1)
        self.assertEqual(room.status, RoomState.SCHEDULED)
        self.assertEqual(room.event_template, self.template)
    
    def test_schedule_room_invalid_time(self):
        """测试预约过去时间的房间"""
        self.authenticate_user(self.user1)
        
        past_time = timezone.now() - timedelta(hours=1)
        schedule_data = {
            'name': '无效时间房间',
            'template_id': self.template.id,
            'scheduled_start_time': past_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn('error', data)

    def test_schedule_room_missing_template(self):
        """测试使用不存在的模板预约房间"""
        self.authenticate_user(self.user1)

        future_time = timezone.now() + timedelta(hours=2)
        schedule_data = {
            'name': '缺失模板房间',
            'template_id': 99999,  # 不存在的模板ID
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 1
        }

        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_schedule_room_subscription_limits(self):
        """测试订阅等级限制"""
        # 免费用户测试
        self.authenticate_user(self.user1)

        future_time = timezone.now() + timedelta(hours=1)
        schedule_data = {
            'name': '免费用户房间',
            'template_id': self.template.id,
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 25  # 超过免费用户限制的24小时
        }

        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        # 应该成功创建，但持续时长会被限制
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        room = Room.objects.get(room_code=response.json()['room_code'])
        self.assertEqual(room.duration_hours, 2)  # 免费用户限制为2小时


class TestRoomStateTransitions(CalendarSystemTestCase):
    """测试房间状态转换"""
    
    def test_scheduled_to_open_transition(self):
        """测试预约房间激活为开启状态"""
        # 创建预约房间
        past_time = timezone.now() - timedelta(minutes=5)  # 5分钟前应该开始
        room = Room.objects.create(
            room_code='SCHED1',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=past_time,
            duration_hours=2
        )
        
        # 模拟系统激活房间
        from core.services.room_manager import RoomManager
        room_manager = RoomManager()
        
        success, message = room_manager.transition_room_state_sync(
            room.room_code, 
            RoomState.OPEN
        )
        
        self.assertTrue(success)
        
        # 验证状态转换
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.OPEN)
    
    def test_room_lifecycle_complete(self):
        """测试完整的房间生命周期"""
        # 1. 创建预约房间
        room = Room.objects.create(
            room_code='LIFE01',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=2
        )
        
        from core.services.room_manager import RoomManager
        room_manager = RoomManager()
        
        # 2. 激活为开启状态
        success, _ = room_manager.transition_room_state_sync(
            room.room_code, RoomState.OPEN
        )
        self.assertTrue(success)
        
        # 3. 房主加入，转为准备状态
        success, _ = room_manager.transition_room_state_sync(
            room.room_code, RoomState.READY
        )
        self.assertTrue(success)
        
        # 4. 开始活动
        success, _ = room_manager.transition_room_state_sync(
            room.room_code, RoomState.IN_PROGRESS
        )
        self.assertTrue(success)
        
        # 5. 结束活动
        success, _ = room_manager.transition_room_state_sync(
            room.room_code, RoomState.ENDED
        )
        self.assertTrue(success)
        
        # 6. 关闭房间
        success, _ = room_manager.transition_room_state_sync(
            room.room_code, RoomState.CLOSED
        )
        self.assertTrue(success)
        
        # 验证最终状态
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.CLOSED)
    
    def test_invalid_state_transition(self):
        """测试无效的状态转换"""
        room = Room.objects.create(
            room_code='INVALID',
            host=self.user1,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=timezone.now() + timedelta(hours=1),
            duration_hours=1
        )
        
        from core.services.room_manager import RoomManager
        room_manager = RoomManager()
        
        # 尝试从SCHEDULED直接跳转到IN_PROGRESS（无效转换）
        success, message = room_manager.transition_room_state_sync(
            room.room_code, RoomState.IN_PROGRESS
        )
        
        self.assertFalse(success)
        self.assertIn('Invalid state transition', message)
        
        # 验证状态没有改变
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.SCHEDULED)


class TestRoomParticipants(CalendarSystemTestCase):
    """测试房间参与者管理"""
    
    def test_add_participant_to_room(self):
        """测试添加参与者到房间"""
        room = Room.objects.create(
            room_code='PART01',
            host=self.user1,
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 添加参与者
        participant = room.add_participant(self.user2, role='participant')
        
        self.assertIsNotNone(participant)
        self.assertEqual(participant.user, self.user2)
        self.assertEqual(participant.role, 'participant')
        self.assertTrue(participant.is_active)
        
        # 验证参与者计数
        self.assertEqual(room.get_participant_count(), 1)
    
    def test_remove_participant_from_room(self):
        """测试从房间移除参与者"""
        room = Room.objects.create(
            room_code='PART02',
            host=self.user1,
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 添加参与者
        room.add_participant(self.user2)
        self.assertEqual(room.get_participant_count(), 1)
        
        # 移除参与者
        success = room.remove_participant(self.user2)
        self.assertTrue(success)
        self.assertEqual(room.get_participant_count(), 0)
    
    def test_room_capacity_limits(self):
        """测试房间容量限制"""
        room = Room.objects.create(
            room_code='LIMIT1',
            host=self.user1,
            event_template=self.template,
            status=RoomState.OPEN,
            max_participants=1,  # 限制为1人
            duration_hours=2
        )
        
        # 添加第一个参与者（应该成功）
        participant1 = room.add_participant(self.user2)
        self.assertIsNotNone(participant1)
        
        # 尝试添加第二个参与者（应该失败，因为已满）
        user3 = User.objects.create_user(
            username='testuser3',
            password='testpass123'
        )
        
        # 检查房间是否已满
        self.assertTrue(room.is_full())
        self.assertFalse(room.can_join())


if __name__ == '__main__':
    pytest.main([__file__])
