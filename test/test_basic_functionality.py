"""
基本功能测试

测试数据库优化后的基本功能是否正常工作
"""

from django.test import TestCase
from django.contrib.auth.models import Group
from rest_framework.test import APITestCase
from rest_framework import status

from core.models import User, Room, RoomParticipant
from events.models import EventTemplate, EventStep


class BasicFunctionalityTest(APITestCase):
    """测试基本功能"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.user
        )
        
        # 创建测试步骤
        self.step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
    
    def test_user_authentication(self):
        """测试用户认证"""
        # 测试登录
        response = self.client.post('/api/token/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        
        # 验证JWT包含订阅信息
        token_data = response.data
        self.assertIn('access', token_data)
    
    def test_room_creation(self):
        """测试房间创建"""
        # 登录用户
        self.client.force_authenticate(user=self.user)
        
        # 创建房间
        response = self.client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('room_code', response.data)
        self.assertEqual(response.data['host'], self.user.username)
        
        # 验证房主参与者记录已创建
        room_code = response.data['room_code']
        room = Room.objects.get(room_code=room_code)
        host_participant = room.room_participants.filter(
            user=self.user, 
            role=RoomParticipant.ROLE_HOST
        ).first()
        self.assertIsNotNone(host_participant)
    
    def test_room_joining(self):
        """测试加入房间"""
        # 创建另一个用户
        participant = User.objects.create_user(
            username='participant',
            password='testpass123'
        )
        
        # 创建房间
        room = Room.objects.create(
            host=self.user,
            room_code='TEST01',
            event_template=self.template
        )
        
        # 创建房主参与记录
        RoomParticipant.objects.create(
            room=room,
            user=self.user,
            role=RoomParticipant.ROLE_HOST
        )
        
        # 参与者加入房间
        self.client.force_authenticate(user=participant)
        response = self.client.post('/api/rooms/join/', {
            'room_code': 'TEST01'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证参与者记录已创建
        participant_record = room.room_participants.filter(
            user=participant,
            role=RoomParticipant.ROLE_PARTICIPANT
        ).first()
        self.assertIsNotNone(participant_record)
        
        # 验证房间参与者数量
        self.assertEqual(room.get_participant_count(), 2)
    
    def test_room_serialization(self):
        """测试房间序列化"""
        # 创建房间和参与者
        room = Room.objects.create(
            host=self.user,
            room_code='TEST02',
            event_template=self.template
        )
        
        # 创建参与者记录
        host_participant = RoomParticipant.objects.create(
            room=room,
            user=self.user,
            role=RoomParticipant.ROLE_HOST,
            score=10
        )
        
        participant2 = User.objects.create_user(
            username='participant2',
            password='testpass123'
        )
        
        regular_participant = RoomParticipant.objects.create(
            room=room,
            user=participant2,
            role=RoomParticipant.ROLE_PARTICIPANT,
            score=5
        )
        
        # 登录并获取房间信息
        self.client.force_authenticate(user=self.user)
        response = self.client.post('/api/rooms/join/', {
            'room_code': 'TEST02'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证序列化数据
        participants = response.data['participants']
        self.assertEqual(len(participants), 2)
        
        # 验证参与者信息包含新字段
        for participant in participants:
            self.assertIn('username', participant)
            self.assertIn('role', participant)
            self.assertIn('score', participant)
            self.assertIn('is_ready', participant)
            self.assertIn('joined_at', participant)
    
    def test_user_groups(self):
        """测试用户组功能"""
        # 创建用户组（如果不存在）
        regular_group, _ = Group.objects.get_or_create(name='Regular Users')
        
        # 测试用户组方法
        self.user.groups.add(regular_group)
        self.assertTrue(self.user.is_in_group('Regular Users'))
        self.assertFalse(self.user.is_administrator())
        self.assertFalse(self.user.is_staff_member())
        self.assertEqual(self.user.get_user_role(), 'user')
    
    def test_score_management(self):
        """测试得分管理"""
        # 创建房间和参与者
        room = Room.objects.create(
            host=self.user,
            room_code='TEST03',
            event_template=self.template
        )
        
        participant = RoomParticipant.objects.create(
            room=room,
            user=self.user,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        # 测试得分操作
        self.assertEqual(participant.score, 0)
        
        participant.add_score(10)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 10)
        
        participant.add_score(5)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 15)
        
        participant.reset_score()
        participant.refresh_from_db()
        self.assertEqual(participant.score, 0)


if __name__ == '__main__':
    import unittest
    unittest.main()
