"""
房间模板系统测试集
测试模板权限控制、模板管理器、API端点等功能
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.urls import reverse

from events.models import EventTemplate, EventStep

User = get_user_model()


class TemplateSystemTestCase(APITestCase):
    """模板系统测试基类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建不同订阅等级的测试用户
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        
        # 创建测试模板
        self.template1 = EventTemplate.objects.create(
            name='免费用户模板',
            description='免费用户创建的模板',
            creator=self.free_user
        )
        
        self.template2 = EventTemplate.objects.create(
            name='Pro用户模板',
            description='Pro用户创建的模板',
            creator=self.pro_user
        )
        
        # 为模板添加步骤
        EventStep.objects.create(
            template=self.template1,
            name='自由聊天',
            step_type='FREE_CHAT',
            order=1,
            duration=300
        )
        
        EventStep.objects.create(
            template=self.template2,
            name='你画我猜',
            step_type='GAME_PICTIONARY',
            order=1,
            duration=600
        )
        
        # 设置API客户端
        self.client = APIClient()
    
    def get_auth_token(self, user):
        """获取用户认证token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def authenticate_user(self, user):
        """认证用户"""
        token = self.get_auth_token(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        return token


class TestTemplatePermissions(TemplateSystemTestCase):
    """测试模板权限控制"""
    
    def test_user_can_see_own_templates(self):
        """测试用户可以看到自己创建的模板"""
        self.authenticate_user(self.free_user)
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # 检查返回的模板
        template_names = [t['name'] for t in data['templates']]
        self.assertIn('免费用户模板', template_names)
        
        # 确保用户看不到其他用户的模板
        self.assertNotIn('Pro用户模板', template_names)
    
    def test_user_cannot_see_others_templates(self):
        """测试用户看不到其他用户的模板"""
        self.authenticate_user(self.pro_user)
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Pro用户应该能看到自己的模板
        template_names = [t['name'] for t in data['templates']]
        self.assertIn('Pro用户模板', template_names)
        
        # 但看不到免费用户的模板
        self.assertNotIn('免费用户模板', template_names)
    
    def test_template_api_requires_authentication(self):
        """测试模板API需要认证"""
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_template_response_format(self):
        """测试模板API响应格式"""
        self.authenticate_user(self.free_user)
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # 检查响应结构
        self.assertIn('templates', data)
        self.assertIn('total_count', data)
        self.assertIsInstance(data['templates'], list)
        self.assertIsInstance(data['total_count'], int)
        
        if data['templates']:
            template = data['templates'][0]
            required_fields = ['id', 'name', 'description', 'creator_username', 'created_at', 'steps']
            for field in required_fields:
                self.assertIn(field, template)
            
            # 检查步骤格式
            if template['steps']:
                step = template['steps'][0]
                step_fields = ['id', 'name', 'step_type', 'order', 'duration', 'configuration']
                for field in step_fields:
                    self.assertIn(field, step)


class TestEventTemplateModel(TemplateSystemTestCase):
    """测试EventTemplate模型"""
    
    def test_create_template_with_steps(self):
        """测试创建带步骤的模板"""
        template = EventTemplate.objects.create(
            name='测试模板',
            description='测试描述',
            creator=self.free_user
        )
        
        # 添加多个步骤
        step1 = EventStep.objects.create(
            template=template,
            name='开场白',
            step_type='SPEECH',
            order=1,
            duration=180
        )
        
        step2 = EventStep.objects.create(
            template=template,
            name='游戏环节',
            step_type='GAME_PICTIONARY',
            order=2,
            duration=600
        )
        
        step3 = EventStep.objects.create(
            template=template,
            name='自由讨论',
            step_type='FREE_CHAT',
            order=3,
            duration=300
        )
        
        # 验证模板和步骤
        self.assertEqual(template.steps.count(), 3)
        
        # 验证步骤顺序
        steps = template.steps.all().order_by('order')
        self.assertEqual(steps[0].name, '开场白')
        self.assertEqual(steps[1].name, '游戏环节')
        self.assertEqual(steps[2].name, '自由讨论')
    
    def test_template_string_representation(self):
        """测试模板的字符串表示"""
        template = EventTemplate.objects.create(
            name='字符串测试模板',
            description='测试__str__方法',
            creator=self.pro_user
        )
        
        expected_str = f"字符串测试模板 (by {self.pro_user.username})"
        self.assertEqual(str(template), expected_str)
    
    def test_template_creator_relationship(self):
        """测试模板与创建者的关系"""
        # 验证用户可以访问自己创建的模板
        user_templates = self.free_user.event_templates.all()
        self.assertIn(self.template1, user_templates)
        
        # 验证模板的创建者
        self.assertEqual(self.template1.creator, self.free_user)
        self.assertEqual(self.template2.creator, self.pro_user)


class TestEventStepModel(TemplateSystemTestCase):
    """测试EventStep模型"""
    
    def test_step_types(self):
        """测试步骤类型"""
        template = EventTemplate.objects.create(
            name='步骤类型测试',
            description='测试各种步骤类型',
            creator=self.max_user
        )
        
        # 测试所有步骤类型
        step_types = [
            'GAME_PICTIONARY',
            'FREE_CHAT',
            'PAUSE',
            'SPEECH',
            'CUSTOM',
            'POLL',
            'QNA'
        ]
        
        for i, step_type in enumerate(step_types, 1):
            step = EventStep.objects.create(
                template=template,
                name=f'步骤{i}',
                step_type=step_type,
                order=i,
                duration=300
            )
            self.assertEqual(step.step_type, step_type)
    
    def test_step_ordering(self):
        """测试步骤排序"""
        template = EventTemplate.objects.create(
            name='排序测试模板',
            description='测试步骤排序',
            creator=self.pro_user
        )
        
        # 创建乱序的步骤
        EventStep.objects.create(template=template, name='第三步', step_type='FREE_CHAT', order=3, duration=300)
        EventStep.objects.create(template=template, name='第一步', step_type='SPEECH', order=1, duration=180)
        EventStep.objects.create(template=template, name='第二步', step_type='GAME_PICTIONARY', order=2, duration=600)
        
        # 验证排序
        steps = template.steps.all().order_by('order')
        self.assertEqual(steps[0].name, '第一步')
        self.assertEqual(steps[1].name, '第二步')
        self.assertEqual(steps[2].name, '第三步')
    
    def test_step_configuration(self):
        """测试步骤配置"""
        template = EventTemplate.objects.create(
            name='配置测试模板',
            description='测试步骤配置',
            creator=self.max_user
        )
        
        # 创建带配置的步骤
        config = {
            'rounds': 5,
            'time_per_round': 120,
            'difficulty': 'medium'
        }
        
        step = EventStep.objects.create(
            template=template,
            name='配置步骤',
            step_type='GAME_PICTIONARY',
            order=1,
            duration=600,
            configuration=config
        )
        
        # 验证配置保存
        self.assertEqual(step.configuration, config)
        self.assertEqual(step.configuration['rounds'], 5)
        self.assertEqual(step.configuration['time_per_round'], 120)
    
    def test_step_display_name(self):
        """测试步骤显示名称"""
        template = EventTemplate.objects.create(
            name='显示名称测试',
            description='测试步骤显示名称',
            creator=self.free_user
        )
        
        # 有自定义名称的步骤
        step_with_name = EventStep.objects.create(
            template=template,
            name='自定义名称',
            step_type='FREE_CHAT',
            order=1,
            duration=300
        )
        
        # 没有自定义名称的步骤
        step_without_name = EventStep.objects.create(
            template=template,
            name='',
            step_type='GAME_PICTIONARY',
            order=2,
            duration=600
        )
        
        # 验证显示名称
        self.assertEqual(step_with_name.get_display_name(), '自定义名称')
        self.assertEqual(step_without_name.get_display_name(), step_without_name.get_step_type_display())


class TestTemplateIntegration(TemplateSystemTestCase):
    """测试模板系统集成"""
    
    def test_template_to_room_creation(self):
        """测试从模板创建房间"""
        from core.models import Room, RoomState
        from django.utils import timezone
        from datetime import timedelta
        
        # 创建房间使用模板
        future_time = timezone.now() + timedelta(hours=2)
        
        self.authenticate_user(self.free_user)
        
        schedule_data = {
            'name': '模板集成测试房间',
            'template_id': self.template1.id,
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建
        room_code = response.json()['room_code']
        room = Room.objects.get(room_code=room_code)
        
        self.assertEqual(room.event_template, self.template1)
        self.assertEqual(room.host, self.free_user)
        self.assertEqual(room.status, RoomState.SCHEDULED)
    
    def test_template_step_count_in_api(self):
        """测试API返回的步骤数量"""
        # 为模板添加更多步骤
        EventStep.objects.create(
            template=self.template1,
            name='额外步骤1',
            step_type='PAUSE',
            order=2,
            duration=60
        )
        
        EventStep.objects.create(
            template=self.template1,
            name='额外步骤2',
            step_type='SPEECH',
            order=3,
            duration=180
        )
        
        self.authenticate_user(self.free_user)
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # 找到我们的模板
        template_data = None
        for template in data['templates']:
            if template['name'] == '免费用户模板':
                template_data = template
                break
        
        self.assertIsNotNone(template_data)
        self.assertEqual(len(template_data['steps']), 3)  # 原来1个 + 新增2个


if __name__ == '__main__':
    pytest.main([__file__])
