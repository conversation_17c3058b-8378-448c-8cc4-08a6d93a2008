"""
数据库优化测试用例

测试新的数据库结构：
1. 用户权限组系统
2. RoomParticipant中间模型
3. 得分系统整合
4. 权限检查功能
"""

import unittest
from django.test import TestCase
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import IntegrityError

from core.models import User, Room, RoomParticipant
from events.models import EventTemplate, EventStep


class UserGroupSystemTest(TestCase):
    """测试用户组权限系统"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # 创建测试组（如果不存在）
        self.regular_group, _ = Group.objects.get_or_create(name='Regular Users')
        self.staff_group, _ = Group.objects.get_or_create(name='Staff')
        self.admin_group, _ = Group.objects.get_or_create(name='Administrators')
        self.super_admin_group, _ = Group.objects.get_or_create(name='Super Administrators')
    
    def test_user_group_assignment(self):
        """测试用户组分配"""
        # 测试添加用户到组
        self.user.groups.add(self.regular_group)
        self.assertTrue(self.user.is_in_group('Regular Users'))
        self.assertFalse(self.user.is_in_group('Staff'))
    
    def test_user_role_methods(self):
        """测试用户角色检查方法"""
        # 测试普通用户
        self.user.groups.add(self.regular_group)
        self.assertFalse(self.user.is_administrator())
        self.assertFalse(self.user.is_staff_member())
        self.assertEqual(self.user.get_user_role(), 'user')
        
        # 测试工作人员
        self.user.groups.clear()
        self.user.groups.add(self.staff_group)
        self.assertFalse(self.user.is_administrator())
        self.assertTrue(self.user.is_staff_member())
        self.assertEqual(self.user.get_user_role(), 'staff')
        
        # 测试管理员
        self.user.groups.clear()
        self.user.groups.add(self.admin_group)
        self.assertTrue(self.user.is_administrator())
        self.assertTrue(self.user.is_staff_member())
        self.assertEqual(self.user.get_user_role(), 'admin')
        
        # 测试超级管理员
        self.user.groups.clear()
        self.user.groups.add(self.super_admin_group)
        self.assertTrue(self.user.is_administrator())
        self.assertTrue(self.user.is_staff_member())
        self.assertEqual(self.user.get_user_role(), 'super_admin')


class RoomParticipantModelTest(TestCase):
    """测试RoomParticipant中间模型"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.host = User.objects.create_user(
            username='host',
            password='testpass123'
        )
        self.participant1 = User.objects.create_user(
            username='participant1',
            password='testpass123'
        )
        self.participant2 = User.objects.create_user(
            username='participant2',
            password='testpass123'
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.host
        )
        
        # 创建测试房间
        self.room = Room.objects.create(
            host=self.host,
            room_code='TEST01',
            event_template=self.template
        )
    
    def test_room_participant_creation(self):
        """测试RoomParticipant创建"""
        # 创建房主参与记录
        host_participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.host,
            role=RoomParticipant.ROLE_HOST
        )
        
        self.assertEqual(host_participant.role, RoomParticipant.ROLE_HOST)
        self.assertEqual(host_participant.score, 0)
        self.assertTrue(host_participant.is_active)
        self.assertTrue(host_participant.can_control_game())
        self.assertTrue(host_participant.can_manage_room())
        
        # 创建普通参与者记录
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant1,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        self.assertEqual(participant.role, RoomParticipant.ROLE_PARTICIPANT)
        self.assertFalse(participant.can_control_game())
        self.assertFalse(participant.can_manage_room())
    
    def test_unique_constraint(self):
        """测试唯一性约束"""
        # 创建第一个参与记录
        RoomParticipant.objects.create(
            room=self.room,
            user=self.participant1,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        # 尝试创建重复记录应该失败
        with self.assertRaises(IntegrityError):
            RoomParticipant.objects.create(
                room=self.room,
                user=self.participant1,
                role=RoomParticipant.ROLE_MODERATOR
            )
    
    def test_score_management(self):
        """测试得分管理"""
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant1,
            role=RoomParticipant.ROLE_PARTICIPANT
        )
        
        # 测试增加得分
        participant.add_score(10)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 10)
        
        # 测试再次增加得分
        participant.add_score(5)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 15)
        
        # 测试重置得分
        participant.reset_score()
        participant.refresh_from_db()
        self.assertEqual(participant.score, 0)


class RoomModelTest(TestCase):
    """测试Room模型的新方法"""
    
    def setUp(self):
        """设置测试数据"""
        self.host = User.objects.create_user(
            username='host',
            password='testpass123'
        )
        self.participant1 = User.objects.create_user(
            username='participant1',
            password='testpass123'
        )
        self.participant2 = User.objects.create_user(
            username='participant2',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='测试模板',
            creator=self.host
        )
        
        self.room = Room.objects.create(
            host=self.host,
            room_code='TEST01',
            event_template=self.template
        )
    
    def test_participant_management(self):
        """测试参与者管理方法"""
        # 测试添加参与者
        participant = self.room.add_participant(self.participant1)
        self.assertIsNotNone(participant)
        self.assertEqual(participant.user, self.participant1)
        self.assertEqual(participant.role, RoomParticipant.ROLE_PARTICIPANT)
        
        # 测试获取参与者数量
        self.assertEqual(self.room.get_participant_count(), 1)
        
        # 测试获取参与者列表
        participants = self.room.get_participants()
        self.assertIn(self.participant1, participants)
        
        # 测试重复添加同一用户
        duplicate_participant = self.room.add_participant(self.participant1)
        self.assertEqual(participant, duplicate_participant)
        self.assertEqual(self.room.get_participant_count(), 1)
        
        # 测试移除参与者
        success = self.room.remove_participant(self.participant1)
        self.assertTrue(success)
        self.assertEqual(self.room.get_participant_count(), 0)
        
        # 测试重新添加之前移除的参与者
        readded_participant = self.room.add_participant(self.participant1)
        self.assertIsNotNone(readded_participant)
        self.assertTrue(readded_participant.is_active)
    
    def test_room_capacity(self):
        """测试房间容量检查"""
        # 设置房间最大容量为2
        self.room.max_participants = 2
        self.room.save()
        
        # 添加参与者直到满员
        self.room.add_participant(self.participant1)
        self.room.add_participant(self.participant2)
        
        # 检查房间是否已满
        self.assertTrue(self.room.is_full())
        
        # 尝试添加更多参与者
        participant3 = User.objects.create_user(
            username='participant3',
            password='testpass123'
        )
        
        # 这里应该仍然能添加，因为add_participant方法本身不检查容量
        # 容量检查应该在视图层进行
        self.room.add_participant(participant3)
        self.assertEqual(self.room.get_participant_count(), 3)


if __name__ == '__main__':
    unittest.main()
