import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from core.models import TemplateManager, SystemTemplate
from django.core.cache import cache

User = get_user_model()


class TestSystemTemplates(TestCase):
    """测试系统模板功能"""

    def setUp(self):
        """设置测试数据"""
        # 清除缓存
        cache.clear()

        # 创建测试用户
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass',
            subscription_level=User.SUBSCRIPTION_FREE
        )

        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass',
            subscription_level=User.SUBSCRIPTION_PRO
        )

        # 创建测试系统模板
        self.blank_template = SystemTemplate.objects.create(
            name='空白模板',
            description='一个空白的房间模板，适合自由讨论和临时活动',
            required_subscription=User.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )

        self.pictionary_template = SystemTemplate.objects.create(
            name='你画我猜 (30分钟)',
            description='经典的你画我猜游戏，持续30分钟，适合团队破冰和娱乐',
            required_subscription=User.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '你画我猜游戏',
                        'step_type': 'GAME_PICTIONARY',
                        'order': 1,
                        'duration': 1800,
                        'configuration': {
                            'rounds': 10,
                            'time_per_round': 180,
                            'difficulty': 'medium'
                        }
                    }
                ]
            }
        )

    def test_system_templates_exist(self):
        """测试系统模板是否存在"""
        templates = SystemTemplate.objects.filter(is_active=True)
        self.assertGreaterEqual(templates.count(), 2)
        
        # 检查特定模板
        blank_template = SystemTemplate.objects.filter(name='空白模板').first()
        self.assertIsNotNone(blank_template)
        self.assertEqual(blank_template.required_subscription, User.SUBSCRIPTION_FREE)
        
        pictionary_template = SystemTemplate.objects.filter(name__contains='你画我猜').first()
        self.assertIsNotNone(pictionary_template)
        self.assertEqual(pictionary_template.required_subscription, User.SUBSCRIPTION_FREE)

    def test_get_available_templates_includes_system_templates(self):
        """测试获取可用模板包含系统模板"""
        templates = TemplateManager.get_available_templates_for_user(self.free_user)
        
        # 应该至少有2个系统模板
        system_templates = [t for t in templates if t['type'] == 'system']
        self.assertGreaterEqual(len(system_templates), 2)
        
        # 检查模板ID格式
        for template in system_templates:
            self.assertTrue(template['id'].startswith('system_'))
            self.assertEqual(template['creator_username'], 'System')

    def test_system_templates_appear_first(self):
        """测试系统模板显示在用户模板之前"""
        # 创建一个用户模板
        from events.models import EventTemplate
        user_template = EventTemplate.objects.create(
            name='用户模板',
            description='测试用户模板',
            creator=self.free_user
        )
        
        templates = TemplateManager.get_available_templates_for_user(self.free_user)
        
        # 找到第一个系统模板和第一个用户模板的位置
        first_system_index = None
        first_user_index = None
        
        for i, template in enumerate(templates):
            if template['type'] == 'system' and first_system_index is None:
                first_system_index = i
            elif template['type'] == 'user' and first_user_index is None:
                first_user_index = i
        
        # 系统模板应该在用户模板之前
        if first_system_index is not None and first_user_index is not None:
            self.assertLess(first_system_index, first_user_index)

    def test_get_template_by_id_system_template(self):
        """测试通过ID获取系统模板"""
        # 获取一个系统模板
        system_template = SystemTemplate.objects.filter(is_active=True).first()
        self.assertIsNotNone(system_template)
        
        template_id = f'system_{system_template.id}'
        template_obj, template_type = TemplateManager.get_template_by_id(template_id, self.free_user)
        
        self.assertIsNotNone(template_obj)
        self.assertEqual(template_type, 'system')
        self.assertEqual(template_obj.id, system_template.id)

    def test_system_template_subscription_access(self):
        """测试系统模板的订阅访问控制"""
        # 创建一个需要Pro版本的系统模板
        pro_template = SystemTemplate.objects.create(
            name='Pro专属模板',
            description='需要Pro版本的模板',
            required_subscription=User.SUBSCRIPTION_PRO,
            template_config={'steps': []}
        )
        
        # 免费用户不应该看到Pro模板
        free_templates = TemplateManager.get_available_templates_for_user(self.free_user)
        pro_template_ids = [t['id'] for t in free_templates if 'Pro专属' in t['name']]
        self.assertEqual(len(pro_template_ids), 0)
        
        # Pro用户应该看到Pro模板
        pro_templates = TemplateManager.get_available_templates_for_user(self.pro_user)
        pro_template_ids = [t['id'] for t in pro_templates if 'Pro专属' in t['name']]
        self.assertEqual(len(pro_template_ids), 1)

    def test_system_template_caching(self):
        """测试系统模板缓存功能"""
        # 清除缓存
        cache.clear()
        
        # 第一次调用应该从数据库获取
        templates1 = TemplateManager._get_system_templates_for_user(self.free_user)
        
        # 第二次调用应该从缓存获取
        templates2 = TemplateManager._get_system_templates_for_user(self.free_user)
        
        # 结果应该相同
        self.assertEqual(len(templates1), len(templates2))
        self.assertEqual(templates1, templates2)
        
        # 验证缓存键存在
        cache_key = f'system_templates_{self.free_user.subscription_level}'
        cached_data = cache.get(cache_key)
        self.assertIsNotNone(cached_data)
