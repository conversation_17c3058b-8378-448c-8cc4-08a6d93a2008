#!/usr/bin/env python3
"""
API修复验证测试脚本
"""

import os
import sys
import django
import requests
import json
import time

# 设置Django环境
sys.path.append('/home/<USER>/Desktop/program/project/Tuanzi')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from events.models import EventTemplate, EventStep

User = get_user_model()

BASE_URL = "http://localhost:8000/api"

def test_api_endpoints():
    """测试API端点"""
    print("🧪 开始测试API端点...")
    
    # 1. 测试健康检查端点（无认证）
    print("1. 测试健康检查端点...")
    try:
        response = requests.get(f"{BASE_URL}/health-check/")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ 健康检查端点正确要求认证")
        else:
            print(f"   ❌ 意外的响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 2. 创建测试用户并获取token
    print("2. 创建测试用户并获取token...")
    try:
        # 创建用户
        user, created = User.objects.get_or_create(
            username='apitest',
            defaults={
                'email': '<EMAIL>',
                'subscription_level': User.SUBSCRIPTION_FREE
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        
        # 获取token
        login_data = {
            'username': 'apitest',
            'password': 'testpass123'
        }
        response = requests.post(f"{BASE_URL}/token/", json=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data['access']
            print(f"   ✅ 成功获取token")
            return access_token
        else:
            print(f"   ❌ 获取token失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 创建用户或获取token失败: {e}")
        return None

def test_authenticated_endpoints(token):
    """测试需要认证的端点"""
    if not token:
        print("❌ 没有有效token，跳过认证测试")
        return
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # 3. 测试健康检查（有认证）
    print("3. 测试认证后的健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health-check/", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查成功: {data.get('user')}")
        else:
            print(f"   ❌ 健康检查失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 4. 测试房间创建
    print("4. 测试房间创建...")
    try:
        # 确保有可用的模板
        template, created = EventTemplate.objects.get_or_create(
            name='API测试模板',
            defaults={
                'description': '用于API测试的模板',
                'creator': User.objects.get(username='apitest')
            }
        )
        
        # 添加测试步骤
        step, created = EventStep.objects.get_or_create(
            template=template,
            order=1,
            defaults={
                'step_type': EventStep.STEP_FREE_CHAT,
                'duration': 300
            }
        )
        
        # 创建房间
        room_data = {'template_id': template.id}
        response = requests.post(f"{BASE_URL}/rooms/create/", json=room_data, headers=headers)
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 201:
            room_info = response.json()
            room_code = room_info.get('room_code')
            print(f"   ✅ 房间创建成功: {room_code}")
            return room_code
        else:
            print(f"   ❌ 房间创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 房间创建过程出错: {e}")
        return None

def test_room_join(token, room_code):
    """测试房间加入"""
    if not token or not room_code:
        print("❌ 缺少token或房间代码，跳过加入测试")
        return
    
    print("5. 测试房间加入...")
    try:
        headers = {'Authorization': f'Bearer {token}'}
        join_data = {'room_code': room_code}
        response = requests.post(f"{BASE_URL}/rooms/join/", json=join_data, headers=headers)
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            room_info = response.json()
            print(f"   ✅ 房间加入成功: {room_info.get('room_code')}")
        else:
            print(f"   ❌ 房间加入失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 房间加入过程出错: {e}")

def test_invalid_token():
    """测试无效token处理"""
    print("6. 测试无效token处理...")
    try:
        headers = {'Authorization': 'Bearer invalid_token_here'}
        response = requests.get(f"{BASE_URL}/health-check/", headers=headers)
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 401:
            data = response.json()
            print(f"   ✅ 正确拒绝无效token: {data.get('code')}")
            if data.get('redirect_to_login'):
                print("   ✅ 正确设置重定向标志")
        else:
            print(f"   ❌ 意外响应: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 API修复验证测试开始")
    print("=" * 60)
    
    # 测试基本端点
    token = test_api_endpoints()
    
    # 测试认证端点
    room_code = test_authenticated_endpoints(token)
    
    # 测试房间加入
    test_room_join(token, room_code)
    
    # 测试错误处理
    test_invalid_token()
    
    print("\n" + "=" * 60)
    print("✅ API测试完成")
    print("=" * 60)
    print("\n💡 提示:")
    print("1. 确保Django服务器正在运行: python manage.py runserver")
    print("2. 检查日志输出以验证详细的错误处理")
    print("3. 前端应该能够正常创建和加入房间")

if __name__ == "__main__":
    main()
