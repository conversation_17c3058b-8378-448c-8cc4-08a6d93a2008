# core/tests/test_room_manager.py

import asyncio
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from asgiref.sync import sync_to_async

from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import room_manager
from core.exceptions import (
    RoomNotFoundException,
    RoomFullException,
    RoomExpiredException,
    InvalidStateTransitionException
)
from events.models import EventTemplate, EventStep

User = get_user_model()

class RoomManagerTestCase(TransactionTestCase):
    """房间管理器测试用例"""

    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.host_user = User.objects.create_user(
            username='testhost',
            email='<EMAIL>',
            password='testpass123'
        )

        self.participant_user = User.objects.create_user(
            username='testparticipant',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.host_user
        )

        # 添加测试步骤
        EventStep.objects.create(
            template=self.template,
            step_type=EventStep.STEP_FREE_CHAT,
            order=1,
            duration=300
        )

    def test_create_room_sync(self):
        """测试同步创建房间"""
        room_data = {
            'host': self.host_user,
            'template': self.template,
            'max_participants': 10
        }

        room = room_manager.create_room_sync(**room_data)

        self.assertIsNotNone(room)
        self.assertEqual(room.host, self.host_user)
        self.assertEqual(room.event_template, self.template)
        self.assertEqual(room.max_participants, 10)
        self.assertEqual(room.status, RoomState.OPEN)

    def test_join_room_sync(self):
        """测试同步加入房间"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 用户加入房间
        success, message = room_manager.join_room_sync(
            room.room_code,
            self.participant_user
        )

        self.assertTrue(success)
        self.assertIn('成功加入房间', message)

        # 验证参与者已添加
        participant = RoomParticipant.objects.filter(
            room=room,
            user=self.participant_user,
            is_active=True
        ).first()

        self.assertIsNotNone(participant)
        self.assertEqual(participant.state, UserState.JOINED)

    def test_leave_room_sync(self):
        """测试同步离开房间"""
        # 创建房间并加入
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        room_manager.join_room_sync(room.room_code, self.participant_user)

        # 离开房间
        success, message = room_manager.leave_room_sync(
            room.room_code,
            self.participant_user
        )

        self.assertTrue(success)
        self.assertIn('成功离开房间', message)

        # 验证参与者已移除
        participant = RoomParticipant.objects.filter(
            room=room,
            user=self.participant_user,
            is_active=True
        ).first()

        self.assertIsNone(participant)

    def test_room_state_transition_sync(self):
        """测试同步房间状态转换"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 转换状态到IN_PROGRESS
        success, message = room_manager.transition_room_state_sync(
            room.room_code,
            RoomState.IN_PROGRESS
        )

        self.assertTrue(success)

        # 验证状态已更新
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.IN_PROGRESS)

    def test_invalid_state_transition(self):
        """测试无效状态转换"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 尝试无效转换（从OPEN直接到ENDED）
        success, message = room_manager.transition_room_state_sync(
            room.room_code,
            RoomState.ENDED
        )

        self.assertFalse(success)
        self.assertIn('Invalid state transition', message)

    def test_room_capacity_limit(self):
        """测试房间容量限制"""
        # 创建容量为1的房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template,
            max_participants=1
        )

        # 第一个用户加入（应该成功）
        success, message = room_manager.join_room_sync(
            room.room_code,
            self.participant_user
        )
        self.assertTrue(success)

        # 创建第二个用户
        user2 = User.objects.create_user(
            username='testuser2',
            password='testpass123'
        )

        # 第二个用户尝试加入（应该失败）
        success, message = room_manager.join_room_sync(
            room.room_code,
            user2
        )
        self.assertFalse(success)
        self.assertIn('房间已满', message)

    def test_room_not_found(self):
        """测试房间不存在的情况"""
        success, message = room_manager.join_room_sync(
            'NONEXIST',
            self.participant_user
        )

        self.assertFalse(success)
        self.assertIn('Room not found', message)

    def test_duplicate_join(self):
        """测试重复加入房间"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 第一次加入
        success, message = room_manager.join_room_sync(
            room.room_code,
            self.participant_user
        )
        self.assertTrue(success)

        # 第二次加入（应该失败）
        success, message = room_manager.join_room_sync(
            room.room_code,
            self.participant_user
        )
        self.assertFalse(success)
        self.assertIn('您已在此房间中', message)

    def test_get_room_info_sync(self):
        """测试获取房间信息"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 获取房间信息
        room_info = room_manager.get_room_info_sync(room.room_code)

        self.assertIsNotNone(room_info)
        self.assertEqual(room_info['room_code'], room.room_code)
        self.assertEqual(room_info['host_username'], self.host_user.username)
        self.assertEqual(room_info['status'], room.status)
        self.assertEqual(room_info['participant_count'], 0)

    def test_room_cleanup(self):
        """测试房间清理"""
        # 创建过期房间
        expired_time = timezone.now() - timedelta(hours=3)
        room = Room.objects.create(
            room_code='EXPIRED',
            host=self.host_user,
            event_template=self.template,
            status=RoomState.OPEN,
            expires_at=expired_time
        )

        # 执行清理
        cleaned_count = room_manager.cleanup_expired_rooms_sync()

        self.assertGreater(cleaned_count, 0)

        # 验证房间已关闭
        room.refresh_from_db()
        self.assertEqual(room.status, RoomState.CLOSED)

    def test_host_heartbeat(self):
        """测试房主心跳"""
        # 创建房间
        room = room_manager.create_room_sync(
            host=self.host_user,
            template=self.template
        )

        # 发送心跳
        success = room_manager.update_host_heartbeat_sync(
            room.room_code,
            self.host_user
        )

        self.assertTrue(success)

        # 验证心跳时间已更新
        self.assertIn(room.room_code, room_manager._host_heartbeats)

    def test_room_expiry_check(self):
        """测试房间过期检查"""
        # 创建即将过期的房间
        near_expiry = timezone.now() + timedelta(minutes=5)
        room = Room.objects.create(
            room_code='NEAREXP',
            host=self.host_user,
            event_template=self.template,
            status=RoomState.OPEN,
            expires_at=near_expiry
        )

        # 检查房间是否过期
        is_expired = room.is_expired()
        self.assertFalse(is_expired)

        # 修改为已过期
        room.expires_at = timezone.now() - timedelta(minutes=1)
        room.save()

        is_expired = room.is_expired()
        self.assertTrue(is_expired)
