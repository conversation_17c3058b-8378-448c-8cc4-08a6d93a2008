#!/usr/bin/env python3
"""
简单的JWT测试脚本
"""

import requests
import json
from jwt import decode as jwt_decode

# 服务器URL
BASE_URL = 'http://localhost:8000/api'

def test_jwt_subscription():
    """测试JWT中的订阅信息"""
    print("=== 测试JWT订阅信息 ===")
    
    # 1. 注册一个新用户
    register_data = {
        'username': 'testuser_jwt',
        'password': 'testpass123'
    }
    
    response = requests.post(f'{BASE_URL}/register/', json=register_data)
    print(f"注册响应: {response.status_code}")
    
    if response.status_code != 201:
        print(f"注册失败: {response.text}")
        return
    
    # 2. 登录获取JWT
    login_data = {
        'username': 'testuser_jwt',
        'password': 'testpass123'
    }
    
    response = requests.post(f'{BASE_URL}/token/', json=login_data)
    print(f"登录响应: {response.status_code}")
    
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return
    
    token_data = response.json()
    access_token = token_data['access']
    
    print(f"获得JWT token: {access_token[:50]}...")
    
    # 3. 解码JWT查看载荷（不验证签名，仅用于测试）
    try:
        # 注意：这里我们不验证签名，仅用于查看载荷内容
        decoded = jwt_decode(access_token, options={"verify_signature": False})
        print(f"JWT载荷: {json.dumps(decoded, indent=2)}")
        
        # 检查是否包含subscription_level
        if 'subscription_level' in decoded:
            print(f"✅ 找到订阅等级: {decoded['subscription_level']}")
        else:
            print("❌ JWT载荷中未找到subscription_level字段")
            
    except Exception as e:
        print(f"解码JWT失败: {e}")

def test_room_creation_limits():
    """测试房间创建限制"""
    print("\n=== 测试房间创建限制 ===")
    
    # 使用之前创建的用户登录
    login_data = {
        'username': 'testuser_jwt',
        'password': 'testpass123'
    }
    
    response = requests.post(f'{BASE_URL}/token/', json=login_data)
    if response.status_code != 200:
        print("登录失败")
        return
    
    token = response.json()['access']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取可用的模板
    response = requests.get('http://localhost:8000/api/events/templates/', headers=headers)
    print(f"获取模板响应: {response.status_code}")
    
    if response.status_code == 200:
        templates = response.json()
        if templates:
            template_id = templates[0]['id']
            print(f"使用模板ID: {template_id}")
            
            # 尝试创建房间
            room_data = {'template_id': template_id}
            response = requests.post(f'{BASE_URL}/rooms/create/', 
                                   json=room_data, headers=headers)
            
            print(f"创建房间响应: {response.status_code}")
            print(f"响应内容: {response.json()}")
            
            if response.status_code == 201:
                room_info = response.json()
                print(f"✅ 房间创建成功: {room_info['room_code']}")
            else:
                print(f"❌ 房间创建失败")
        else:
            print("没有可用的模板")
    else:
        print(f"获取模板失败: {response.status_code}")

if __name__ == '__main__':
    try:
        test_jwt_subscription()
        test_room_creation_limits()
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
