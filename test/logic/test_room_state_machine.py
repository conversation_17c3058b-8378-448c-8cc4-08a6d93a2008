"""
Tests for room state machine logic - the core of the system.
"""

import pytest
import asyncio
from django.contrib.auth import get_user_model
from channels.db import database_sync_to_async

from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import room_manager
from core.exceptions import RoomSystemException
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.logic
class TestRoomStateMachine:
    """Test the room state machine transitions and business logic."""

    async def setup_test_data(self):
        """Set up test data for each test method."""
        # Create test users
        self.host_user = await database_sync_to_async(User.objects.create_user)(
            username='state_host',
            password='testpass123',
            subscription_level='Pro'
        )

        self.participant1 = await database_sync_to_async(User.objects.create_user)(
            username='state_participant1',
            password='testpass123',
            subscription_level='Free'
        )

        self.participant2 = await database_sync_to_async(User.objects.create_user)(
            username='state_participant2',
            password='testpass123',
            subscription_level='Free'
        )

        # Create test template
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='State Test Template',
                description='Template for state machine testing',
                creator=self.host_user
            )

            EventStep.objects.create(
                template=template,
                order=1,
                step_type=EventStep.STEP_GAME_PICTIONARY,
                duration=300,
                name='Pictionary Round'
            )

            EventStep.objects.create(
                template=template,
                order=2,
                step_type=EventStep.STEP_FREE_CHAT,
                duration=180,
                name='Free Chat'
            )

            return template

        self.template = await create_template()

        # Create test room
        self.room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )

    async def cleanup_test_data(self):
        """Clean up after each test method."""
        await database_sync_to_async(Room.objects.filter(room_code__startswith='STATE').delete)()
        await database_sync_to_async(EventTemplate.objects.filter(name__contains='State Test').delete)()
        await database_sync_to_async(User.objects.filter(username__startswith='state_').delete)()
    
    @pytest.mark.asyncio
    async def test_initial_room_state(self):
        """Test that new rooms start in WAITING state."""
        await self.setup_test_data()
        try:
            assert self.room.status == RoomState.OPEN
            assert self.room.host == self.host_user
            assert self.room.current_step_order == 0
        finally:
            await self.cleanup_test_data()

    @pytest.mark.asyncio
    async def test_user_join_waiting_room(self):
        """Test users can join rooms in WAITING state."""
        success, message = await room_manager.join_room(self.room.room_code, self.participant1)

        assert success is True
        assert "successfully joined" in message.lower()

        # Verify participant state
        room = await room_manager.get_room(self.room.room_code)
        participants = await database_sync_to_async(list)(
            room.room_participants.all()
        )

        participant = next(p for p in participants if p.user == self.participant1)
        assert participant.state == UserState.JOINED
        assert participant.role == RoomParticipant.ROLE_PARTICIPANT

    @pytest.mark.asyncio
    async def test_multiple_users_join(self):
        """Test multiple users can join the same room."""
        # Join participant 1
        success1, _ = await room_manager.join_room(self.room.room_code, self.participant1)
        assert success1 is True

        # Join participant 2
        success2, _ = await room_manager.join_room(self.room.room_code, self.participant2)
        assert success2 is True

        # Verify both participants are in room
        room = await room_manager.get_room(self.room.room_code)
        participant_count = await database_sync_to_async(room.room_participants.count)()
        assert participant_count == 2
    
    @pytest.mark.asyncio
    async def test_transition_waiting_to_active(self):
        """Test transitioning from WAITING to ACTIVE state."""
        # Add participants first
        await room_manager.join_room(self.room.room_code, self.participant1)
        await room_manager.join_room(self.room.room_code, self.participant2)

        # Transition to ACTIVE (only host can do this)
        success, message = await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )

        assert success is True
        assert "transitioned to ACTIVE" in message

        # Verify room state
        room = await room_manager.get_room(self.room.room_code)
        assert room.status == RoomState.IN_PROGRESS

    @pytest.mark.asyncio
    async def test_non_host_cannot_start_game(self):
        """Test that non-host users cannot transition room to ACTIVE."""
        await room_manager.join_room(self.room.room_code, self.participant1)

        # Participant tries to start game
        success, message = await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.participant1
        )

        assert success is False
        assert "permission" in message.lower() or "host" in message.lower()

        # Verify room is still in WAITING state
        room = await room_manager.get_room(self.room.room_code)
        assert room.status == RoomState.OPEN
    
    @pytest.mark.asyncio
    async def test_join_active_room_becomes_spectator(self):
        """Test that users joining ACTIVE rooms become spectators."""
        # Start the game first
        await room_manager.join_room(self.room.room_code, self.participant1)
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        # New user joins active room
        success, message = await room_manager.join_room(self.room.room_code, self.participant2)
        
        assert success is True
        
        # Verify participant2 is a spectator
        room = await room_manager.get_room(self.room.room_code)
        participants = await database_sync_to_async(list)(
            room.room_participants.all()
        )
        
        participant2 = next(p for p in participants if p.user == self.participant2)
        assert participant2.state == UserState.SPECTATING
    
    @pytest.mark.asyncio
    async def test_transition_active_to_review(self):
        """Test transitioning from ACTIVE to REVIEW state."""
        # Set up active room
        await room_manager.join_room(self.room.room_code, self.participant1)
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        # Transition to REVIEW
        success, message = await room_manager.transition_room_state(
            self.room.room_code, RoomState.ENDED, self.host_user
        )
        
        assert success is True
        
        # Verify room state and review timestamp
        room = await room_manager.get_room(self.room.room_code)
        assert room.status == RoomState.ENDED
        assert room.review_started_at is not None
    
    @pytest.mark.asyncio
    async def test_transition_review_to_closed(self):
        """Test transitioning from REVIEW to CLOSED state."""
        # Set up review room
        await room_manager.join_room(self.room.room_code, self.participant1)
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.ENDED, self.host_user
        )
        
        # Transition to CLOSED
        success, message = await room_manager.transition_room_state(
            self.room.room_code, RoomState.CLOSED, self.host_user
        )
        
        assert success is True
        
        # Verify room state and closed timestamp
        room = await room_manager.get_room(self.room.room_code)
        assert room.status == RoomState.CLOSED
        assert room.closed_at is not None
    
    @pytest.mark.asyncio
    async def test_invalid_state_transitions(self):
        """Test that invalid state transitions are rejected."""
        # Try to go directly from WAITING to REVIEW (should fail)
        success, message = await room_manager.transition_room_state(
            self.room.room_code, RoomState.ENDED, self.host_user
        )
        
        assert success is False
        assert "invalid transition" in message.lower() or "cannot transition" in message.lower()
        
        # Verify room is still in WAITING state
        room = await room_manager.get_room(self.room.room_code)
        assert room.status == RoomState.OPEN
    
    @pytest.mark.asyncio
    async def test_cannot_join_closed_room(self):
        """Test that users cannot join closed rooms."""
        # Close the room
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.ENDED, self.host_user
        )
        await room_manager.transition_room_state(
            self.room.room_code, RoomState.CLOSED, self.host_user
        )
        
        # Try to join closed room
        success, message = await room_manager.join_room(self.room.room_code, self.participant1)
        
        assert success is False
        assert "closed" in message.lower() or "cannot join" in message.lower()
    
    @pytest.mark.asyncio
    async def test_user_leave_room(self):
        """Test user leaving room updates state correctly."""
        # Join and then leave
        await room_manager.join_room(self.room.room_code, self.participant1)
        
        success, message = await room_manager.leave_room(self.room.room_code, self.participant1)
        
        assert success is True
        
        # Verify participant is marked as left
        room = await room_manager.get_room(self.room.room_code)
        participants = await database_sync_to_async(list)(
            room.room_participants.all()
        )
        
        participant = next(p for p in participants if p.user == self.participant1)
        assert participant.left_at is not None
    
    @pytest.mark.asyncio
    async def test_host_transfer_on_leave(self):
        """Test that host is transferred when current host leaves."""
        # Add participants
        await room_manager.join_room(self.room.room_code, self.participant1)
        await room_manager.join_room(self.room.room_code, self.participant2)
        
        # Host leaves
        success, message = await room_manager.leave_room(self.room.room_code, self.host_user)
        
        assert success is True
        
        # Verify new host was assigned
        room = await room_manager.get_room(self.room.room_code)
        assert room.host != self.host_user
        assert room.host in [self.participant1, self.participant2]
