"""
Simplified tests for room state machine logic - the core of the system.
"""

import pytest
import asyncio
from django.contrib.auth import get_user_model
from channels.db import database_sync_to_async

from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import room_manager
from core.exceptions import RoomSystemException
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.logic
class TestRoomStateMachineSimple:
    """Test the room state machine transitions and business logic."""
    
    @pytest.mark.asyncio
    async def test_complete_room_lifecycle(self):
        """Test a complete room lifecycle from creation to closure."""
        # Setup
        host_user = await database_sync_to_async(User.objects.create_user)(
            username='lifecycle_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        participant1 = await database_sync_to_async(User.objects.create_user)(
            username='lifecycle_participant1',
            password='testpass123',
            subscription_level='Free'
        )
        
        participant2 = await database_sync_to_async(User.objects.create_user)(
            username='lifecycle_participant2',
            password='testpass123',
            subscription_level='Free'
        )
        
        # Create test template
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='Lifecycle Test Template',
                description='Template for lifecycle testing',
                creator=host_user
            )
            
            EventStep.objects.create(
                template=template,
                order=1,
                step_type=EventStep.STEP_GAME_PICTIONARY,
                duration=300,
                name='Pictionary Round'
            )
            
            return template
        
        template = await create_template()
        
        try:
            # Step 1: Create room
            room = await room_manager.create_room(
                host=host_user,
                template_id=template.id
            )
            
            # Verify initial state
            assert room.status == RoomState.OPEN
            assert room.host == host_user
            
            # Step 2: Users join room
            success1, message1 = await room_manager.join_room(room.room_code, participant1)
            assert success1 is True
            assert "成功加入房间" in message1
            
            success2, message2 = await room_manager.join_room(room.room_code, participant2)
            assert success2 is True
            
            # Verify participants are in room
            updated_room = await room_manager.get_room(room.room_code)
            participant_count = await database_sync_to_async(
                updated_room.room_participants.filter(left_at__isnull=True).count
            )()
            assert participant_count == 3  # host + 2 participants
            
            # Step 3: Transition to ACTIVE (only host can do this)
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.IN_PROGRESS, host_user
            )
            assert success is True
            
            # Verify room state
            updated_room = await room_manager.get_room(room.room_code)
            assert updated_room.status == RoomState.IN_PROGRESS
            
            # Step 4: Test that non-host cannot transition state
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.ENDED, participant1
            )
            assert success is False
            assert "只有房主" in message
            
            # Step 5: Test late joiner becomes spectator
            late_joiner = await database_sync_to_async(User.objects.create_user)(
                username='late_joiner',
                password='testpass123'
            )
            
            success, message = await room_manager.join_room(room.room_code, late_joiner)
            assert success is True
            
            # Verify late joiner is spectator
            @database_sync_to_async
            def get_late_participant():
                return updated_room.room_participants.filter(
                    left_at__isnull=True, user=late_joiner
                ).select_related('user').first()

            late_participant = await get_late_participant()
            assert late_participant is not None
            assert late_participant.state == UserState.SPECTATING
            
            # Step 6: Transition to REVIEW
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.ENDED, host_user
            )
            assert success is True
            
            updated_room = await room_manager.get_room(room.room_code)
            assert updated_room.status == RoomState.ENDED
            assert updated_room.review_started_at is not None
            
            # Step 7: Transition to CLOSED
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.CLOSED, host_user
            )
            assert success is True
            
            updated_room = await room_manager.get_room(room.room_code)
            assert updated_room.status == RoomState.CLOSED
            assert updated_room.closed_at is not None
            
            # Step 8: Test cannot join closed room
            new_user = await database_sync_to_async(User.objects.create_user)(
                username='new_user_test',
                password='testpass123'
            )
            
            success, message = await room_manager.join_room(room.room_code, new_user)
            assert success is False
            assert "已关闭" in message
            
        finally:
            # Cleanup
            await database_sync_to_async(Room.objects.filter(room_code=room.room_code).delete)()
            await database_sync_to_async(EventTemplate.objects.filter(name__contains='Lifecycle Test').delete)()
            await database_sync_to_async(User.objects.filter(username__startswith='lifecycle_').delete)()
            await database_sync_to_async(User.objects.filter(username__in=['late_joiner', 'new_user_test']).delete)()
    
    @pytest.mark.asyncio
    async def test_invalid_state_transitions(self):
        """Test that invalid state transitions are rejected."""
        # Setup
        host_user = await database_sync_to_async(User.objects.create_user)(
            username='invalid_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='Invalid Test Template',
                description='Template for invalid transition testing',
                creator=host_user
            )
            return template
        
        template = await create_template()
        
        try:
            # Create room
            room = await room_manager.create_room(
                host=host_user,
                template_id=template.id
            )
            
            # Try to go directly from WAITING to REVIEW (should fail)
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.ENDED, host_user
            )
            
            assert success is False
            assert "无法从" in message or "转换" in message
            
            # Verify room is still in WAITING state
            updated_room = await room_manager.get_room(room.room_code)
            assert updated_room.status == RoomState.OPEN
            
        finally:
            # Cleanup
            await database_sync_to_async(Room.objects.filter(room_code=room.room_code).delete)()
            await database_sync_to_async(EventTemplate.objects.filter(name__contains='Invalid Test').delete)()
            await database_sync_to_async(User.objects.filter(username='invalid_host').delete)()
    
    @pytest.mark.asyncio
    async def test_host_transfer_on_leave(self):
        """Test that host is transferred when current host leaves."""
        # Setup
        host_user = await database_sync_to_async(User.objects.create_user)(
            username='transfer_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        participant1 = await database_sync_to_async(User.objects.create_user)(
            username='transfer_participant1',
            password='testpass123',
            subscription_level='Free'
        )
        
        participant2 = await database_sync_to_async(User.objects.create_user)(
            username='transfer_participant2',
            password='testpass123',
            subscription_level='Free'
        )
        
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='Transfer Test Template',
                description='Template for host transfer testing',
                creator=host_user
            )
            return template
        
        template = await create_template()
        
        try:
            # Create room and add participants
            room = await room_manager.create_room(
                host=host_user,
                template_id=template.id
            )
            
            await room_manager.join_room(room.room_code, participant1)
            await room_manager.join_room(room.room_code, participant2)
            
            # Host leaves
            success, message = await room_manager.leave_room(room.room_code, host_user)
            assert success is True
            
            # Verify new host was assigned
            updated_room = await room_manager.get_room(room.room_code)
            assert updated_room.host != host_user
            assert updated_room.host in [participant1, participant2]
            
        finally:
            # Cleanup
            await database_sync_to_async(Room.objects.filter(room_code=room.room_code).delete)()
            await database_sync_to_async(EventTemplate.objects.filter(name__contains='Transfer Test').delete)()
            await database_sync_to_async(User.objects.filter(username__startswith='transfer_').delete)()
