#!/usr/bin/env python3
"""
简化的修复验证测试脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/home/<USER>/Desktop/program/project/Tuanzi')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

def test_imports():
    """测试导入是否正常"""
    print("🧪 测试模块导入...")
    
    try:
        from core.middleware.session_management import (
            SessionTimeoutMiddleware,
            WebSocketSessionMiddleware,
            ConnectionTimeoutMiddleware
        )
        print("   ✅ 会话管理中间件导入成功")
    except Exception as e:
        print(f"   ❌ 中间件导入失败: {e}")
        return False
    
    try:
        from core.views import HealthCheckView
        print("   ✅ 健康检查视图导入成功")
    except Exception as e:
        print(f"   ❌ 健康检查视图导入失败: {e}")
        return False
    
    try:
        from core.services.room_manager import room_manager
        print("   ✅ 房间管理器导入成功")
    except Exception as e:
        print(f"   ❌ 房间管理器导入失败: {e}")
        return False
    
    return True

def test_settings():
    """测试设置配置"""
    print("🧪 测试设置配置...")
    
    try:
        from django.conf import settings
        
        # 检查中间件配置
        middleware = settings.MIDDLEWARE
        session_middleware_found = any('SessionTimeoutMiddleware' in m for m in middleware)
        connection_middleware_found = any('ConnectionTimeoutMiddleware' in m for m in middleware)
        
        if session_middleware_found:
            print("   ✅ 会话超时中间件已配置")
        else:
            print("   ❌ 会话超时中间件未配置")
        
        if connection_middleware_found:
            print("   ✅ 连接超时中间件已配置")
        else:
            print("   ❌ 连接超时中间件未配置")
        
        # 检查会话设置
        session_timeout = getattr(settings, 'SESSION_TIMEOUT_MINUTES', None)
        if session_timeout:
            print(f"   ✅ 会话超时设置: {session_timeout}分钟")
        else:
            print("   ❌ 会话超时设置未配置")
        
        return session_middleware_found and connection_middleware_found and session_timeout
        
    except Exception as e:
        print(f"   ❌ 设置检查失败: {e}")
        return False

def test_url_patterns():
    """测试URL配置"""
    print("🧪 测试URL配置...")
    
    try:
        from django.urls import reverse
        
        # 测试健康检查URL
        health_url = reverse('health-check')
        print(f"   ✅ 健康检查URL: {health_url}")
        
        # 测试房间创建URL
        create_url = reverse('room-create')
        print(f"   ✅ 房间创建URL: {create_url}")
        
        # 测试房间加入URL
        join_url = reverse('room-join')
        print(f"   ✅ 房间加入URL: {join_url}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ URL配置检查失败: {e}")
        return False

def test_database_models():
    """测试数据库模型"""
    print("🧪 测试数据库模型...")
    
    try:
        from core.models import Room, RoomParticipant, RoomState, UserState
        
        # 检查枚举
        print(f"   ✅ 房间状态: {list(RoomState)}")
        print(f"   ✅ 用户状态: {list(UserState)}")
        
        # 检查模型字段
        room_fields = [f.name for f in Room._meta.fields]
        required_fields = ['review_started_at', 'last_activity_at']
        
        for field in required_fields:
            if field in room_fields:
                print(f"   ✅ Room模型包含字段: {field}")
            else:
                print(f"   ❌ Room模型缺少字段: {field}")
        
        participant_fields = [f.name for f in RoomParticipant._meta.fields]
        required_participant_fields = ['state', 'left_at']
        
        for field in required_participant_fields:
            if field in participant_fields:
                print(f"   ✅ RoomParticipant模型包含字段: {field}")
            else:
                print(f"   ❌ RoomParticipant模型缺少字段: {field}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库模型检查失败: {e}")
        return False

def test_logging_configuration():
    """测试日志配置"""
    print("🧪 测试日志配置...")
    
    try:
        import logging
        
        # 测试日志记录器
        logger = logging.getLogger('core.views')
        logger.info("测试日志消息")
        print("   ✅ 日志记录器工作正常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 日志配置检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 修复验证测试开始")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("设置配置", test_settings),
        ("URL配置", test_url_patterns),
        ("数据库模型", test_database_models),
        ("日志配置", test_logging_configuration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统修复成功。")
        print("\n💡 下一步:")
        print("1. 启动Django服务器: python manage.py runserver")
        print("2. 测试前端房间创建功能")
        print("3. 验证会话超时机制")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关配置。")

if __name__ == "__main__":
    main()
