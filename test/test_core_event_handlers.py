"""
测试新的面向对象环节处理器
"""
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from django.test import TestCase
from django.contrib.auth import get_user_model
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async

from core.models import Room, RoomParticipant
from events.models import EventTemplate, EventStep
from games.models import Game, PictionaryGame
from core.event_handlers import (
    BaseEventHandler,
    PictionaryEventHandler,
    FreeChatEventHandler,
    EventHandlerFactory
)
from core.consumers import RoomConsumer

User = get_user_model()


class MockConsumer:
    """模拟的 WebSocket 消费者"""
    def __init__(self):
        self.channel_layer = AsyncMock()
        self.room_group_name = "test_room_group"
        self.send_error = AsyncMock()


class BaseEventHandlerTest(TestCase):
    """基础环节处理器测试"""
    
    def setUp(self):
        self.room_code = "TEST123"
        self.consumer = MockConsumer()
        
    def test_abstract_methods(self):
        """测试抽象方法不能直接实例化"""
        with self.assertRaises(TypeError):
            BaseEventHandler(self.room_code, self.consumer)
    
    async def test_common_methods(self):
        """测试通用方法"""
        # 创建一个具体的处理器来测试通用方法
        handler = PictionaryEventHandler(self.room_code, self.consumer)
        
        # 测试广播方法
        test_data = {"type": "test", "message": "hello"}
        await handler.broadcast_to_room(test_data)
        
        # 验证消息被发送到正确的组
        handler.consumer.channel_layer.group_send.assert_called_once_with(
            handler.consumer.room_group_name,
            test_data
        )


class PictionaryEventHandlerTest(TestCase):
    """你画我猜环节处理器测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='Test Description',
            creator=self.user
        )
        
        self.room = Room.objects.create(
            room_code='PICT123',
            host=self.user,
            event_template=self.template
        )
        
        self.consumer = MockConsumer()
        self.handler = PictionaryEventHandler(self.room.room_code, self.consumer)
    
    async def test_start_event(self):
        """测试开始你画我猜环节"""
        config = {
            'rounds': 3,
            'time_per_round': 120,
            'words': ['苹果', '汽车', '房子']
        }
        
        await self.handler.start_event(config)
        
        # 验证游戏对象被创建
        game = await database_sync_to_async(Game.objects.filter)(
            room=self.room,
            game_type='PICTIONARY'
        )
        game = await database_sync_to_async(game.first)()
        self.assertIsNotNone(game)
        
        # 验证你画我猜游戏被创建
        pictionary_game = await database_sync_to_async(PictionaryGame.objects.filter)(
            game=game
        )
        pictionary_game = await database_sync_to_async(pictionary_game.first)()
        self.assertIsNotNone(pictionary_game)
        self.assertEqual(pictionary_game.total_rounds, 3)
    
    async def test_handle_drawing_action(self):
        """测试处理绘画动作"""
        # 先开始游戏
        config = {'rounds': 1, 'time_per_round': 120}
        await self.handler.start_event(config)
        
        # 模拟绘画动作
        drawing_data = {
            'action': 'draw',
            'x': 100,
            'y': 150,
            'color': '#000000',
            'size': 5
        }
        
        await self.handler.handle_action('drawing', drawing_data, self.user)
        
        # 验证绘画数据被广播
        self.consumer.channel_layer.group_send.assert_called()
    
    async def test_handle_guess_action(self):
        """测试处理猜词动作"""
        # 先开始游戏
        config = {'rounds': 1, 'time_per_round': 120}
        await self.handler.start_event(config)
        
        # 模拟猜词
        guess_data = {
            'action': 'guess',
            'word': '苹果'
        }
        
        await self.handler.handle_action('guess', guess_data, self.user)
        
        # 验证猜词被处理
        self.consumer.channel_layer.group_send.assert_called()
    
    async def test_end_event(self):
        """测试结束你画我猜环节"""
        # 先开始游戏
        config = {'rounds': 1, 'time_per_round': 120}
        await self.handler.start_event(config)
        
        # 结束游戏
        await self.handler.end_event()
        
        # 验证游戏状态被更新
        game = await database_sync_to_async(Game.objects.filter)(
            room=self.room,
            game_type='PICTIONARY'
        )
        game = await database_sync_to_async(game.first)()
        self.assertEqual(game.status, 'FINISHED')


class FreeChatEventHandlerTest(TestCase):
    """自由聊天环节处理器测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='chatuser',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Chat Template',
            description='Chat Description',
            creator=self.user
        )
        
        self.room = Room.objects.create(
            room_code='CHAT123',
            host=self.user,
            event_template=self.template
        )
        
        self.consumer = MockConsumer()
        self.handler = FreeChatEventHandler(self.room.room_code, self.consumer)
    
    async def test_start_event(self):
        """测试开始自由聊天环节"""
        config = {
            'topic': '今天天气真好',
            'duration': 300
        }
        
        await self.handler.start_event(config)
        
        # 验证聊天环节开始消息被广播
        self.consumer.channel_layer.group_send.assert_called()
        
        # 获取调用参数
        call_args = self.consumer.channel_layer.group_send.call_args[0][1]
        self.assertEqual(call_args['type'], 'chat_started')
        self.assertEqual(call_args['topic'], '今天天气真好')
    
    async def test_handle_message_action(self):
        """测试处理聊天消息"""
        # 先开始聊天
        config = {'topic': '测试话题', 'duration': 300}
        await self.handler.start_event(config)
        
        # 重置mock以清除之前的调用
        self.consumer.channel_layer.group_send.reset_mock()
        
        # 发送聊天消息
        message_data = {
            'action': 'message',
            'content': '大家好！'
        }
        
        await self.handler.handle_action('message', message_data, self.user)
        
        # 验证消息被广播
        self.consumer.channel_layer.group_send.assert_called()
        call_args = self.consumer.channel_layer.group_send.call_args[0][1]
        self.assertEqual(call_args['type'], 'chat_message')
        self.assertEqual(call_args['message'], '大家好！')
        self.assertEqual(call_args['username'], self.user.username)
    
    async def test_end_event(self):
        """测试结束自由聊天环节"""
        # 先开始聊天
        config = {'topic': '测试话题', 'duration': 300}
        await self.handler.start_event(config)
        
        # 结束聊天
        await self.handler.end_event()
        
        # 验证结束消息被广播
        self.consumer.channel_layer.group_send.assert_called()


class EventHandlerFactoryTest(TestCase):
    """环节处理器工厂测试"""
    
    def setUp(self):
        self.room_code = "FACTORY123"
        self.consumer = MockConsumer()
    
    def test_create_pictionary_handler(self):
        """测试创建你画我猜处理器"""
        handler = EventHandlerFactory.create_handler(
            'GAME_PICTIONARY',
            self.room_code,
            self.consumer
        )
        
        self.assertIsInstance(handler, PictionaryEventHandler)
        self.assertEqual(handler.room_code, self.room_code)
        self.assertEqual(handler.consumer, self.consumer)
    
    def test_create_free_chat_handler(self):
        """测试创建自由聊天处理器"""
        handler = EventHandlerFactory.create_handler(
            'FREE_CHAT',
            self.room_code,
            self.consumer
        )
        
        self.assertIsInstance(handler, FreeChatEventHandler)
        self.assertEqual(handler.room_code, self.room_code)
        self.assertEqual(handler.consumer, self.consumer)
    
    def test_create_unknown_handler(self):
        """测试创建未知类型处理器"""
        with self.assertRaises(ValueError):
            EventHandlerFactory.create_handler(
                'UNKNOWN_TYPE',
                self.room_code,
                self.consumer
            )
    
    def test_get_supported_types(self):
        """测试获取支持的环节类型"""
        supported_types = EventHandlerFactory.get_supported_types()
        
        self.assertIn('GAME_PICTIONARY', supported_types)
        self.assertIn('FREE_CHAT', supported_types)
        self.assertIsInstance(supported_types, list)


class EventHandlerIntegrationTest(TestCase):
    """环节处理器集成测试"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='player1',
            password='testpass123'
        )
        
        self.user2 = User.objects.create_user(
            username='player2',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Integration Template',
            description='Integration Test',
            creator=self.user1
        )
        
        # 创建包含多个步骤的模板
        EventStep.objects.create(
            template=self.template,
            name='开场聊天',
            step_type='FREE_CHAT',
            order=1,
            duration=180,
            configuration={'topic': '自我介绍'}
        )
        
        EventStep.objects.create(
            template=self.template,
            name='你画我猜游戏',
            step_type='GAME_PICTIONARY',
            order=2,
            duration=600,
            configuration={'rounds': 3, 'time_per_round': 120}
        )
        
        self.room = Room.objects.create(
            room_code='INTEG123',
            host=self.user1,
            event_template=self.template
        )
        
        # 添加参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role='host'
        )
        
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user2,
            role='participant'
        )
    
    async def test_sequential_event_handling(self):
        """测试顺序处理多个环节"""
        consumer = MockConsumer()
        
        # 第一个环节：自由聊天
        chat_handler = EventHandlerFactory.create_handler(
            'FREE_CHAT',
            self.room.room_code,
            consumer
        )
        
        await chat_handler.start_event({'topic': '自我介绍', 'duration': 180})
        
        # 模拟聊天交互
        await chat_handler.handle_action(
            'message',
            {'action': 'message', 'content': '大家好，我是玩家1'},
            self.user1
        )
        
        await chat_handler.end_event()
        
        # 第二个环节：你画我猜
        pictionary_handler = EventHandlerFactory.create_handler(
            'GAME_PICTIONARY',
            self.room.room_code,
            consumer
        )
        
        await pictionary_handler.start_event({
            'rounds': 3,
            'time_per_round': 120
        })
        
        # 模拟游戏交互
        await pictionary_handler.handle_action(
            'drawing',
            {'action': 'draw', 'x': 100, 'y': 100, 'color': '#000000'},
            self.user1
        )
        
        await pictionary_handler.handle_action(
            'guess',
            {'action': 'guess', 'word': '苹果'},
            self.user2
        )
        
        await pictionary_handler.end_event()
        
        # 验证所有环节都被正确处理
        self.assertTrue(consumer.channel_layer.group_send.called)
        self.assertGreater(consumer.channel_layer.group_send.call_count, 4)
