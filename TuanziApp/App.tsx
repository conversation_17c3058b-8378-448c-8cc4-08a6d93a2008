import 'react-native-gesture-handler';

import React from 'react';
import { AuthProvider } from './src/auth/AuthContext';
import { SubscriptionProvider } from './src/contexts/SubscriptionContext';
import { RootNavigator } from './src/navigation';

const App = () => {
  return (
    <AuthProvider>
      <SubscriptionProvider>
        <RootNavigator />
      </SubscriptionProvider>
    </AuthProvider>
  );
};

export default App;
