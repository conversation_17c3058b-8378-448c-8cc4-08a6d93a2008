/**
 * 通用样式系统
 *
 * 基于主题系统的通用样式，提供一致的设计语言
 */

import { StyleSheet } from 'react-native';
import { theme } from './theme';

// 保持向后兼容的颜色导出
export const colors = {
    primary: theme.colors.primary,
    background: theme.colors.surface,
    text: theme.colors.textPrimary,
    lightGray: theme.colors.gray100,
    mediumGray: theme.colors.gray300,
    darkGray: theme.colors.gray600,
    white: theme.colors.white,
    black: theme.colors.black,
    myMessageBubble: '#dcf8c6',
    error: theme.colors.error,
    success: theme.colors.success,
};

export const commonStyles = StyleSheet.create({
    // 安全区域
    safeArea: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },

    // 容器样式
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
        padding: theme.spacing.xl,
    },
    containerCentered: {
        flex: 1,
        backgroundColor: theme.colors.background,
        alignItems: 'center',
        justifyContent: 'center',
        padding: theme.spacing.xl,
    },

    // 文本样式
    title: {
        fontSize: theme.typography.fontSize['4xl'],
        fontWeight: theme.typography.fontWeight.bold,
        marginBottom: theme.spacing['2xl'],
        color: theme.colors.textPrimary,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: theme.typography.fontSize.xl,
        fontWeight: theme.typography.fontWeight.semibold,
        marginTop: theme.spacing.xl,
        marginBottom: theme.spacing.md,
        color: theme.colors.textSecondary,
    },
    text: {
        fontSize: theme.typography.fontSize.base,
        color: theme.colors.textPrimary,
        lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
    },
    textSecondary: {
        fontSize: theme.typography.fontSize.base,
        color: theme.colors.textSecondary,
        lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
    },
    linkText: {
        color: theme.colors.primary,
        marginTop: theme.spacing.xl,
        fontSize: theme.typography.fontSize.lg,
    },

    // 输入框样式
    input: {
        width: '90%',
        height: 44,
        borderColor: theme.colors.border,
        borderWidth: 1,
        borderRadius: theme.borderRadius.md,
        marginBottom: theme.spacing.md,
        paddingHorizontal: theme.spacing.lg,
        fontSize: theme.typography.fontSize.lg,
        backgroundColor: theme.colors.surface,
    },

    // 按钮容器
    buttonContainer: {
        marginTop: theme.spacing.md,
        width: '90%',
    },

    // 卡片样式
    card: {
        backgroundColor: theme.colors.surface,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.lg,
        marginVertical: theme.spacing.sm,
        ...theme.shadows.md,
    },

    // 布局样式
    row: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    column: {
        flexDirection: 'column',
    },
    center: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    spaceBetween: {
        justifyContent: 'space-between',
    },

    // 状态文本
    errorText: {
        color: theme.colors.error,
        fontSize: theme.typography.fontSize.sm,
        marginTop: theme.spacing.xs,
    },
    successText: {
        color: theme.colors.success,
        fontSize: theme.typography.fontSize.sm,
        marginTop: theme.spacing.xs,
    },
    warningText: {
        color: theme.colors.warning,
        fontSize: theme.typography.fontSize.sm,
        marginTop: theme.spacing.xs,
    },
});
