/**
 * 样式工具函数
 * 
 * 提供便捷的样式生成函数，提升开发效率
 */

import { ViewStyle, TextStyle } from 'react-native';
import { theme } from './theme';

/**
 * 生成间距样式
 */
export const spacing = {
  // 外边距
  m: (value: keyof typeof theme.spacing): ViewStyle => ({
    margin: theme.spacing[value],
  }),
  mt: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginTop: theme.spacing[value],
  }),
  mb: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginBottom: theme.spacing[value],
  }),
  ml: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginLeft: theme.spacing[value],
  }),
  mr: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginRight: theme.spacing[value],
  }),
  mx: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginHorizontal: theme.spacing[value],
  }),
  my: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginVertical: theme.spacing[value],
  }),

  // 内边距
  p: (value: keyof typeof theme.spacing): ViewStyle => ({
    padding: theme.spacing[value],
  }),
  pt: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingTop: theme.spacing[value],
  }),
  pb: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingBottom: theme.spacing[value],
  }),
  pl: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingLeft: theme.spacing[value],
  }),
  pr: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingRight: theme.spacing[value],
  }),
  px: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingHorizontal: theme.spacing[value],
  }),
  py: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingVertical: theme.spacing[value],
  }),
};

/**
 * 生成文本样式
 */
export const text = {
  size: (value: keyof typeof theme.typography.fontSize): TextStyle => ({
    fontSize: theme.typography.fontSize[value],
  }),
  weight: (value: keyof typeof theme.typography.fontWeight): TextStyle => ({
    fontWeight: theme.typography.fontWeight[value],
  }),
  color: (value: keyof typeof theme.colors): TextStyle => ({
    color: theme.colors[value],
  }),
  align: (value: 'left' | 'center' | 'right' | 'justify'): TextStyle => ({
    textAlign: value,
  }),
  lineHeight: (value: keyof typeof theme.typography.lineHeight): TextStyle => ({
    lineHeight: theme.typography.lineHeight[value],
  }),
};

/**
 * 生成布局样式
 */
export const layout = {
  flex: (value: number = 1): ViewStyle => ({
    flex: value,
  }),
  flexDirection: (value: 'row' | 'column' | 'row-reverse' | 'column-reverse'): ViewStyle => ({
    flexDirection: value,
  }),
  justifyContent: (value: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly'): ViewStyle => ({
    justifyContent: value,
  }),
  alignItems: (value: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline'): ViewStyle => ({
    alignItems: value,
  }),
  alignSelf: (value: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline'): ViewStyle => ({
    alignSelf: value,
  }),
  position: (value: 'absolute' | 'relative'): ViewStyle => ({
    position: value,
  }),
  width: (value: number | string): ViewStyle => ({
    width: value,
  }),
  height: (value: number | string): ViewStyle => ({
    height: value,
  }),
};

/**
 * 生成边框样式
 */
export const border = {
  radius: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderRadius: theme.borderRadius[value],
  }),
  width: (value: number): ViewStyle => ({
    borderWidth: value,
  }),
  color: (value: keyof typeof theme.colors): ViewStyle => ({
    borderColor: theme.colors[value],
  }),
  style: (value: 'solid' | 'dotted' | 'dashed'): ViewStyle => ({
    borderStyle: value,
  }),
};

/**
 * 生成背景样式
 */
export const background = {
  color: (value: keyof typeof theme.colors): ViewStyle => ({
    backgroundColor: theme.colors[value],
  }),
};

/**
 * 生成阴影样式
 */
export const shadow = {
  sm: theme.shadows.sm,
  md: theme.shadows.md,
  lg: theme.shadows.lg,
  xl: theme.shadows.xl,
};

/**
 * 组合样式工具
 */
export const combine = (...styles: (ViewStyle | TextStyle | undefined)[]): ViewStyle | TextStyle => {
  return Object.assign({}, ...styles.filter(Boolean));
};

/**
 * 条件样式工具
 */
export const when = (condition: boolean, style: ViewStyle | TextStyle): ViewStyle | TextStyle | undefined => {
  return condition ? style : undefined;
};

/**
 * 响应式样式工具（基于屏幕尺寸）
 */
export const responsive = {
  small: (style: ViewStyle | TextStyle): ViewStyle | TextStyle => style,
  medium: (style: ViewStyle | TextStyle): ViewStyle | TextStyle => style,
  large: (style: ViewStyle | TextStyle): ViewStyle | TextStyle => style,
};
