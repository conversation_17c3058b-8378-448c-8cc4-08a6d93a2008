import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { HomeScreen } from '../screens/HomeScreen';
import { RoomScreen } from '../screens/RoomScreen';
import { CreateRoomScreen } from '../screens/CreateRoomScreen';
import { TemplateListScreen } from '../screens/EventDesigner/TemplateListScreen';
import { CreateTemplateScreen } from '../screens/EventDesigner/CreateTemplateScreen';
import { TemplateDetailScreen } from '../screens/EventDesigner/TemplateDetailScreen';
import { AddStepScreen } from '../screens/EventDesigner/AddStepScreen';
import { EditStepScreen } from '../screens/EventDesigner/EditStepScreen';
import { SubscriptionScreen } from '../screens/SubscriptionScreen';
import { CalendarScreen } from '../screens/CalendarScreen';
import { ScheduleRoomScreen } from '../screens/ScheduleRoomScreen';
import { RootStackParamList } from '../types';

// Apply the ParamList type to the navigator
const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator = () => (
    <Stack.Navigator>
        <Stack.Screen 
            name="Home" 
            component={HomeScreen} 
            options={{ title: '主页' }}
        />
        <Stack.Screen
            name="CreateRoom"
            component={CreateRoomScreen}
            options={{ title: '创建房间' }}
        />
        <Stack.Screen 
            name="Room" 
            component={RoomScreen} 
            options={({ route }) => ({ title: `房间: ${route.params.room.room_code}` })}
        />
        <Stack.Screen
            name="EventDesigner"
            component={TemplateListScreen}
            options={{ title: '环节设计器' }}
        />
        <Stack.Screen
            name="CreateTemplate"
            component={CreateTemplateScreen}
            options={{ title: '创建新模板' }}
        />
        <Stack.Screen
            name="TemplateDetail"
            component={TemplateDetailScreen}
            options={{ title: '模板详情' }}
        />
        <Stack.Screen
            name="AddStep"
            component={AddStepScreen}
            options={{ title: '添加新步骤' }}
        />
        <Stack.Screen
            name="EditStep"
            component={EditStepScreen}
            options={{ title: '编辑步骤' }}
        />
        <Stack.Screen
            name="Subscription"
            component={SubscriptionScreen}
            options={{ title: '订阅管理' }}
        />
        <Stack.Screen
            name="Calendar"
            component={CalendarScreen}
            options={{ title: '日历预约' }}
        />
        <Stack.Screen
            name="ScheduleRoom"
            component={ScheduleRoomScreen}
            options={{ title: '预约房间' }}
        />
    </Stack.Navigator>
);
