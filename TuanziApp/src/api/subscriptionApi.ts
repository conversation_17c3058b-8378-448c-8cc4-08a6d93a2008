/**
 * 订阅管理API - 重构版本
 *
 * 功能：
 * - 获取用户订阅信息
 * - 更新订阅等级
 * - 模拟支付流程
 * - 订阅计划管理
 * - 权限验证
 */

import { API_URL } from './client';

export interface SubscriptionInfo {
  current_level: 'Free' | 'Pro' | 'Max';
  username: string;
  subscription_info: {
    [key: string]: {
      max_participants: number;
      duration_hours: number;
    };
  };
}

export interface SubscriptionUpdateResponse {
  message: string;
  new_level?: string;
  access_token?: string;
  refresh_token?: string;
  payment_url?: string;
  order_id?: string;
}

/**
 * 获取用户当前订阅信息
 */
export const getSubscriptionInfo = async (token: string): Promise<SubscriptionInfo> => {
  const response = await fetch(`${API_URL}/api/subscription/`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get subscription info: ${response.status}`);
  }

  return response.json();
};

/**
 * 更新用户订阅等级
 */
export const updateSubscriptionLevel = async (
  token: string,
  targetLevel: 'Free' | 'Pro' | 'Max',
  isDebug: boolean = false
): Promise<SubscriptionUpdateResponse> => {
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // 开发模式添加调试头
  if (isDebug && __DEV__) {
    headers['X-Debug-Mode'] = 'true';
  }

  const response = await fetch(`${API_URL}/api/subscription/`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      target_level: targetLevel,
      is_debug: isDebug,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `Failed to update subscription: ${response.status}`);
  }

  return response.json();
};

/**
 * 模拟支付流程（开发阶段）
 */
export const simulatePayment = async (
  targetLevel: 'Free' | 'Pro' | 'Max',
  amount: number
): Promise<{ success: boolean; orderId: string }> => {
  // 模拟支付延迟
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 模拟支付成功率（开发阶段总是成功）
  return {
    success: true,
    orderId: `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };
};

/**
 * 获取订阅计划信息
 */
export const getSubscriptionPlans = () => {
  return [
    {
      level: 'Free' as const,
      name: '免费版',
      price: 0,
      priceText: '免费',
      features: ['基础游戏功能', '你画我猜', '自由讨论'],
      roomLimit: '10人',
      timeLimit: '2小时',
      color: '#6B7280'
    },
    {
      level: 'Pro' as const,
      name: 'Pro版',
      price: 19.9,
      priceText: '¥19.9/月',
      features: ['所有基础功能', '高级环节类型', '投票功能', '问答环节', '暂停环节'],
      roomLimit: '500人',
      timeLimit: '24小时',
      color: '#3B82F6'
    },
    {
      level: 'Max' as const,
      name: 'Max版',
      price: 39.9,
      priceText: '¥39.9/月',
      features: ['所有Pro功能', '无限制房间', '自定义环节', '优先客服支持'],
      roomLimit: '2000人',
      timeLimit: '72小时',
      color: '#8B5CF6'
    }
  ];
};

/**
 * 验证订阅等级权限
 */
export const checkSubscriptionPermission = (
  userLevel: 'Free' | 'Pro' | 'Max',
  requiredLevel: 'Free' | 'Pro' | 'Max'
): boolean => {
  const levels = ['Free', 'Pro', 'Max'];
  const userLevelIndex = levels.indexOf(userLevel);
  const requiredLevelIndex = levels.indexOf(requiredLevel);
  
  return userLevelIndex >= requiredLevelIndex;
};

/**
 * 获取订阅等级的数值表示（用于比较）
 */
export const getSubscriptionLevelValue = (level: 'Free' | 'Pro' | 'Max'): number => {
  const levelValues = { 'Free': 0, 'Pro': 1, 'Max': 2 };
  return levelValues[level];
};
