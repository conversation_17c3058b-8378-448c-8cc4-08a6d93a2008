import AsyncStorage from '@react-native-async-storage/async-storage';
// --- FIX: Change the import statement ---
import { jwtDecode } from 'jwt-decode';

const tokenKey = 'userToken';
const userKey = 'currentUser';

type DecodedToken = {
    user_id: number;
    username: string;
    subscription_level: 'Free' | 'Pro' | 'Max';
};

export const storeToken = async (token: string) => {
    try {
        await AsyncStorage.setItem(tokenKey, token);
        // --- FIX: Use the new function name ---
        const decoded: DecodedToken = jwtDecode(token);
        await AsyncStorage.setItem(userKey, decoded.username);
    } catch (error) {
        console.log('Error storing the auth token', error);
    }
};

export const getToken = () => AsyncStorage.getItem(tokenKey);

export const getUser = async () => {
    try {
        const token = await getToken();
        if (!token) return null;
        // --- FIX: Use the new function name ---
        const decoded: DecodedToken = jwtDecode(token);
        return {
            token,
            username: decoded.username,
            subscription_level: decoded.subscription_level
        };
    } catch (error) {
        console.log('Error getting user from token', error);
        return null;
    }
};

export const removeToken = async () => {
    try {
        await AsyncStorage.multiRemove([tokenKey, userKey]);
    } catch (error) {
        console.log('Error removing the auth token', error);
    }
};
