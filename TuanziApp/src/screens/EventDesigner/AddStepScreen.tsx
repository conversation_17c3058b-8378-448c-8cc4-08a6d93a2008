import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { addStepToTemplate } from '../../api/eventApi';
import { RootStackParamList } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type AddStepRouteProp = RouteProp<RootStackParamList, 'AddStep'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'AddStep'>;

const STEP_CHOICES = [
    // Basic steps (available to all users)
    { label: '游戏：你画我猜', value: 'GAME_PICTIONARY', premium: false },
    { label: '自由讨论', value: 'FREE_CHAT', premium: false },

    // Premium steps (Pro/Max only)
    { label: '暂停环节', value: 'PAUSE', premium: true },
    { label: '发言环节', value: 'SPEECH', premium: true },
    { label: '自定义环节', value: 'CUSTOM', premium: true },
    { label: '投票环节', value: 'POLL', premium: true },
    { label: '问答环节', value: 'QNA', premium: true },
];

export const AddStepScreen = () => {
    const route = useRoute<AddStepRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();
    const { checkPermission, upgradeSubscription } = useSubscription();
    
    const [name, setName] = useState('');
    const [selectedStep, setSelectedStep] = useState<string | null>(null);
    const [duration, setDuration] = useState('5');
    const [isLoading, setIsLoading] = useState(false);

    const handleStepSelection = (stepValue: string, isPremium: boolean) => {
        // Check if user has permission for premium features
        if (isPremium && !checkPermission('Pro')) {
            Alert.alert(
                '升级到Pro版本',
                '此功能需要Pro版本才能使用。升级后您将获得更多高级环节类型和更大的房间容量。',
                [
                    { text: '取消', style: 'cancel' },
                    {
                        text: '立即升级',
                        onPress: () => {
                            navigation.navigate('Subscription' as any);
                        }
                    },
                    {
                        text: '调试升级',
                        onPress: async () => {
                            if (__DEV__) {
                                const success = await upgradeSubscription('Pro', true);
                                if (success) {
                                    Alert.alert('升级成功', '已升级到Pro版本（调试模式）');
                                }
                            }
                        }
                    }
                ]
            );
            return;
        }
        setSelectedStep(stepValue);
    };

    const handleSave = async () => {
        if (!selectedStep) {
            Alert.alert('错误', '请选择一个环节类型。');
            return;
        }

        const durationNum = parseInt(duration);
        if (isNaN(durationNum) || durationNum <= 0) {
            Alert.alert('错误', '请输入有效的时长（分钟）。');
            return;
        }

        if (!token) return;

        setIsLoading(true);
        try {
            await addStepToTemplate(token, templateId, {
                name: name.trim(),
                step_type: selectedStep,
                duration: durationNum * 60, // Convert minutes to seconds
            });
            Alert.alert('成功', '新步骤已添加！');
            navigation.goBack();
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '添加步骤失败。');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
            <Text style={commonStyles.title}>添加新步骤</Text>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>步骤名称（可选）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="例如：开场破冰游戏"
                    value={name}
                    onChangeText={setName}
                />
                <Text style={styles.hint}>留空将使用默认名称</Text>
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>环节类型</Text>
                {STEP_CHOICES.map(choice => {
                    const isDisabled = choice.premium && !checkPermission('Pro');
                    return (
                        <TouchableOpacity
                            key={choice.value}
                            style={[
                                styles.choiceButton,
                                selectedStep === choice.value && styles.selectedChoice,
                                isDisabled && styles.disabledChoice
                            ]}
                            onPress={() => handleStepSelection(choice.value, choice.premium)}
                            disabled={false} // We handle the logic in handleStepSelection
                        >
                            <View style={styles.choiceContent}>
                                <Text style={[
                                    styles.choiceText,
                                    selectedStep === choice.value && styles.selectedChoiceText,
                                    isDisabled && styles.disabledChoiceText
                                ]}>
                                    {choice.label}
                                </Text>
                                {choice.premium && (
                                    <Text style={[
                                        styles.premiumBadge,
                                        isDisabled && styles.disabledPremiumBadge
                                    ]}>
                                        Pro
                                    </Text>
                                )}
                            </View>
                        </TouchableOpacity>
                    );
                })}
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>时长（分钟）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="5"
                    value={duration}
                    onChangeText={setDuration}
                    keyboardType="numeric"
                />
            </View>

            <View style={styles.buttonContainer}>
                {isLoading ? (
                    <ActivityIndicator size="large" />
                ) : (
                    <Button title="确认添加" onPress={handleSave} disabled={!selectedStep} />
                )}
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    contentContainer: {
        padding: 20,
    },
    section: {
        marginBottom: 25,
    },
    hint: {
        fontSize: 12,
        color: '#888',
        marginTop: 5,
        fontStyle: 'italic',
    },
    choiceButton: {
        width: '100%',
        padding: 15,
        marginVertical: 8,
        backgroundColor: '#fff',
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#e0e0e0',
    },
    selectedChoice: {
        borderColor: '#007AFF',
        backgroundColor: '#e6f2ff',
    },
    disabledChoice: {
        backgroundColor: '#f5f5f5',
        borderColor: '#d0d0d0',
    },
    choiceContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    choiceText: {
        fontSize: 16,
        color: '#333',
        flex: 1,
    },
    selectedChoiceText: {
        color: '#007AFF',
        fontWeight: '600',
    },
    disabledChoiceText: {
        color: '#999',
    },
    premiumBadge: {
        backgroundColor: '#FF6B35',
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        textAlign: 'center',
        minWidth: 40,
    },
    disabledPremiumBadge: {
        backgroundColor: '#ccc',
    },
    buttonContainer: {
        marginTop: 30,
        marginBottom: 40,
    },
});
