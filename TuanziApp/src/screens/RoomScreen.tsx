import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { WEBSOCKET_URL_BASE } from '../api/client';
import { RootStackParamList, EventStep, Message, PictionaryState } from '../types';

import { PictionaryView } from '../components/steps/PictionaryView';
import { ChatView } from '../components/steps/ChatView';
import { LobbyView } from '../components/steps/LobbyView';
import { PathData } from '../components/PictionaryCanvas';
import { ActionPanel } from '../components/ActionPanel';

type RoomScreenRouteProp = RouteProp<RootStackParamList, 'Room'>;

export const RoomScreen = () => {
    const route = useRoute<RoomScreenRouteProp>();
    const initialRoom = route.params.room;
    const { user, token } = useAuth();

    const [currentStep, setCurrentStep] = useState<EventStep | null>(null);
    const [_stepPayload, setStepPayload] = useState<any>(null);
    const [roomStatus, setRoomStatus] = useState(initialRoom.status);
    const [isWsConnected, setIsWsConnected] = useState(false);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const maxReconnectAttempts = 5;
    
    const [isActionPanelVisible, setIsActionPanelVisible] = useState(false);
    
    const [messages, setMessages] = useState<Message[]>([]);
    const [paths, setPaths] = useState<PathData[]>([]);
    const [pictionaryState, setPictionaryState] = useState<PictionaryState | null>(null);
    const localPathIds = useRef<Set<string>>(new Set());
    const drawingThrottle = useRef<{
        lastSentTime: number;
        pendingPath: PathData | null;
        timeoutId: number | null;
    }>({
        lastSentTime: 0,
        pendingPath: null,
        timeoutId: null
    });
    
    const ws = useRef<WebSocket | null>(null);

    const connectWebSocket = useCallback(() => {
        if (!initialRoom || !token) return;

        const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${initialRoom.room_code}/?token=${token}`;
        ws.current = new WebSocket(wsUrl);

        ws.current.onopen = () => {
            console.log('WebSocket connected');
            setIsWsConnected(true);
            setReconnectAttempts(0);
        };

        ws.current.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            setIsWsConnected(false);

            if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
                const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                setTimeout(() => {
                    setReconnectAttempts(prev => prev + 1);
                    connectWebSocket();
                }, timeout);
            } else if (reconnectAttempts >= maxReconnectAttempts) {
                Alert.alert('连接失败', '无法连接到服务器，请检查网络连接后重新进入房间。');
            }
        };

        ws.current.onerror = (e: any) => {
            console.error('WebSocket error:', e.message);
        };

        ws.current.onmessage = (e: any) => {
            try {
                const data = JSON.parse(e.data);

                switch (data.type) {
                    case 'step_started':
                        setCurrentStep(data.payload.step_info);
                        setStepPayload(data.payload);
                        setRoomStatus(data.payload.room_status);
                        setMessages([]);
                        setPaths([]);
                        localPathIds.current.clear();
                        if (data.payload.step_info.step_type === 'GAME_PICTIONARY') {
                            setPictionaryState(data.payload);
                        }
                        break;
                    case 'round_over':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus('WAITING');
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();

                        const alertTitle = data.payload.timeout ? "时间到!" :
                                         data.payload.winner ? "猜对了!" : "游戏结束!";
                        const alertMessage = data.payload.timeout ? `时间到! 正确答案是: ${data.payload.word}` :
                                           data.payload.winner ? `${data.payload.winner} 猜中了答案: ${data.payload.word}` :
                                           `正确答案是: ${data.payload.word}`;

                        if (user?.username === initialRoom.host) {
                            Alert.alert(
                                alertTitle,
                                alertMessage + "\n\n是否再开始一轮？",
                                [
                                    { text: "结束", style: "cancel" },
                                    {
                                        text: "再来一轮",
                                        onPress: () => sendWebSocketMessage('restart_game', { game_type: 'PICTIONARY' })
                                    }
                                ]
                            );
                        } else {
                            Alert.alert(alertTitle, alertMessage);
                        }
                        break;
                    case 'event_finished':
                        Alert.alert("活动结束", data.payload.message);
                        setRoomStatus('FINISHED');
                        setCurrentStep(null);
                        break;
                    case 'error':
                        Alert.alert("操作失败", data.payload.message);
                        break;
                    case 'chat_message':
                        setMessages(prev => [data.payload, ...prev]);
                        break;
                    case 'drawing_data':
                        const incomingPath = data.payload.path_data;
                        if (!localPathIds.current.has(incomingPath.id)) {
                            setPaths(prev => [...prev, incomingPath]);
                        }
                        break;
                    case 'step_timeout':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus(data.payload.room_status);
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();
                        Alert.alert("时间到!", "环节时间已结束");
                        break;
                    default:
                        console.warn('Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
    }, [initialRoom, token, reconnectAttempts, maxReconnectAttempts]);

    useEffect(() => {
        connectWebSocket();
        return () => {
            if (ws.current) {
                ws.current.close(1000, 'Component unmounting');
            }
            if (drawingThrottle.current.timeoutId) {
                clearTimeout(drawingThrottle.current.timeoutId);
            }
        };
    }, [connectWebSocket]);

    const sendWebSocketMessage = (action: string, payload: object = {}) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({ action, payload }));
        }
    };

    const handleNextStep = () => {
        sendWebSocketMessage('next_step');
    };

    const handleDraw = (pathData: PathData) => {
        localPathIds.current.add(pathData.id);
        setPaths(prev => [...prev, pathData]);

        const now = Date.now();
        const THROTTLE_MS = 50;

        if (now - drawingThrottle.current.lastSentTime >= THROTTLE_MS) {
            drawingThrottle.current.lastSentTime = now;
            sendWebSocketMessage('send_drawing', { path_data: pathData });
        } else {
            drawingThrottle.current.pendingPath = pathData;

            if (drawingThrottle.current.timeoutId) {
                clearTimeout(drawingThrottle.current.timeoutId);
            }

            const remainingTime = THROTTLE_MS - (now - drawingThrottle.current.lastSentTime);
            drawingThrottle.current.timeoutId = setTimeout(() => {
                if (drawingThrottle.current.pendingPath) {
                    drawingThrottle.current.lastSentTime = Date.now();
                    sendWebSocketMessage('send_drawing', {
                        path_data: drawingThrottle.current.pendingPath
                    });
                    drawingThrottle.current.pendingPath = null;
                }
                drawingThrottle.current.timeoutId = null;
            }, remainingTime);
        }
    };

    const handleSendMessage = (message: string) => {
        sendWebSocketMessage('send_message', { message });
    };

    const renderCurrentStep = () => {
        if (roomStatus === 'FINISHED') return <LobbyView isFinished={true} />;

        if (!currentStep || roomStatus === 'WAITING') {
            const isHost = user?.username === initialRoom.host;
            return <LobbyView isHost={isHost} onNextStep={handleNextStep} isConnected={isWsConnected} />;
        }

        switch (currentStep.step_type) {
            case 'GAME_PICTIONARY':
                if (!pictionaryState) {
                    return <View style={styles.container}><ActivityIndicator size="large" /></View>;
                }
                return (
                    <PictionaryView
                        isDrawer={user?.username === pictionaryState.drawer}
                        pictionaryState={pictionaryState}
                        paths={paths}
                        messages={messages}
                        onDraw={handleDraw}
                        onSendMessage={handleSendMessage}
                    />
                );
            case 'FREE_CHAT':
                return (
                    <ChatView
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        roomHost={initialRoom.host}
                    />
                );
            default:
                return <LobbyView isHost={user?.username === initialRoom.host} onNextStep={handleNextStep} isConnected={isWsConnected} />;
        }
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                {/* --- 核心更改：新的标题栏 --- */}
                <View style={styles.header}>
                    <View style={styles.headerTitleContainer}>
                        <Text style={styles.title}>房间: {initialRoom.room_code}</Text>
                    </View>
                    <TouchableOpacity
                        style={styles.headerButton}
                        onPress={() => setIsActionPanelVisible(true)}
                    >
                        <Text style={styles.headerButtonIcon}>⚙️</Text>
                    </TouchableOpacity>
                </View>
                
                {renderCurrentStep()}

                {/* 全局操作面板 (渲染逻辑不变) */}
                <ActionPanel
                    isVisible={isActionPanelVisible}
                    onClose={() => setIsActionPanelVisible(false)}
                    roomHost={initialRoom.host}
                    onNextStep={handleNextStep}
                />
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: '#fff' },
    container: { flex: 1 },
    // --- 新增样式 ---
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTitleContainer: {
        flex: 1, // 占据剩余空间，确保标题居中
        alignItems: 'center',
    },
    title: { 
        fontSize: 22, 
        fontWeight: 'bold',
        // 调整左边距以更好地居中
        marginLeft: 40, 
    },
    headerButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerButtonIcon: {
        fontSize: 24,
    },
});
