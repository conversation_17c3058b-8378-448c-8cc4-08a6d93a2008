/**
 * 订阅管理页面 - 重构版本
 *
 * 功能：
 * - 显示当前订阅状态
 * - 展示所有可用的订阅计划
 * - 处理订阅升级/降级
 * - 开发模式下提供调试功能
 * - 使用模块化组件提升可维护性
 *
 * 特性：
 * - 模块化组件设计
 * - 统一的主题系统
 * - 可复用的UI组件
 * - 清晰的组件分离
 */

import React, { useState, useEffect } from 'react';
import { Text, Alert } from 'react-native';
import { useSubscription } from '../contexts/SubscriptionContext';
import { commonStyles } from '../styles/commonStyles';
import {
  SubscriptionPlanCard,
  SubscriptionStatusCard,
  DebugModeToggle,
  Card,
  Screen
} from '../components';

// 订阅等级类型定义
type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

/**
 * 订阅管理页面主组件 - 重构版本
 */
export const SubscriptionScreen = () => {
  // 获取认证和订阅相关的状态和方法
  const { subscriptionInfo, upgradeSubscription, getPlans } = useSubscription();

  // 本地状态管理
  const [isDebugMode, setIsDebugMode] = useState(__DEV__);  // 调试模式开关
  const [forceRender, setForceRender] = useState(0);        // 强制重新渲染计数器

  // 获取所有订阅计划和当前计划
  const plans = getPlans();
  const currentPlan = plans.find(plan => plan.level === (subscriptionInfo?.current_level || 'Free')) || plans[0];

  /**
   * 监听订阅信息变化，强制重新渲染
   * 解决订阅等级切换后UI不更新的问题
   */
  useEffect(() => {
    console.log('SubscriptionScreen: subscriptionInfo changed:', subscriptionInfo);
    setForceRender(prev => prev + 1);
  }, [subscriptionInfo]);

  /**
   * 处理订阅升级请求
   */
  const handleUpgrade = async (targetLevel: SubscriptionLevel) => {
    if (targetLevel === subscriptionInfo?.current_level) {
      Alert.alert('提示', '您已经是该订阅等级了');
      return;
    }

    const targetPlan = plans.find(plan => plan.level === targetLevel);
    if (!targetPlan) return;

    const isUpgrade = plans.findIndex(p => p.level === targetLevel) >
                     plans.findIndex(p => p.level === (subscriptionInfo?.current_level || 'Free'));

    Alert.alert(
      isUpgrade ? '升级订阅' : '降级订阅',
      `确定要${isUpgrade ? '升级' : '降级'}到${targetPlan.name}吗？\n价格：${targetPlan.priceText}`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: `确认${isUpgrade ? '升级' : '降级'}`,
          onPress: async () => {
            const success = await upgradeSubscription(targetLevel, false);
            if (success) {
              Alert.alert(
                `${isUpgrade ? '升级' : '降级'}成功`,
                `已成功${isUpgrade ? '升级' : '降级'}到${targetPlan.name}！`
              );
              setForceRender(prev => prev + 1);
            }
          }
        }
      ]
    );
  };

  /**
   * 处理调试模式的等级切换
   */
  const handleDebugLevelChange = async (level: SubscriptionLevel) => {
    if (!isDebugMode) return;

    Alert.alert(
      '开发调试',
      `切换到${level}等级？（仅开发模式）`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确认',
          onPress: async () => {
            const success = await upgradeSubscription(level, true);
            if (success) {
              Alert.alert('调试功能', `已切换到${level}等级（调试模式）`);
              setForceRender(prev => prev + 1);
            }
          }
        }
      ]
    );
  };

  /**
   * 渲染订阅计划卡片 - 使用模块化组件
   */
  const renderPlanCard = (plan: any) => {
    const isCurrentPlan = plan.level === subscriptionInfo?.current_level;
    const isUpgrade = plans.findIndex(p => p.level === plan.level) >
                     plans.findIndex(p => p.level === (subscriptionInfo?.current_level || 'Free'));

    return (
      <SubscriptionPlanCard
        key={`${plan.level}-${forceRender}`}
        plan={plan}
        isCurrentPlan={isCurrentPlan}
        isUpgrade={isUpgrade}
        isDebugMode={isDebugMode}
        onUpgrade={handleUpgrade}
        onDebugChange={handleDebugLevelChange}
      />
    );
  };

  return (
    <Screen>
      <Text style={commonStyles.title}>订阅管理</Text>

      {/* 当前订阅状态 - 使用模块化组件 */}
      <SubscriptionStatusCard currentPlan={currentPlan} />

      {/* 开发调试开关 - 使用模块化组件 */}
      <DebugModeToggle
        isDebugMode={isDebugMode}
        onToggle={setIsDebugMode}
      />

      {/* 订阅计划 */}
      <Text style={commonStyles.subtitle}>选择订阅计划</Text>
      {plans.map(renderPlanCard)}

      {/* 订阅说明 */}
      <Card style={{ marginTop: 20, marginBottom: 40 }}>
        <Text style={[commonStyles.subtitle, { marginTop: 0, marginBottom: 12 }]}>
          订阅说明
        </Text>
        <Text style={[commonStyles.textSecondary, { marginBottom: 8 }]}>
          • 订阅立即生效，按月计费
        </Text>
        <Text style={[commonStyles.textSecondary, { marginBottom: 8 }]}>
          • 可随时取消，到期后自动降级
        </Text>
        <Text style={[commonStyles.textSecondary, { marginBottom: 8 }]}>
          • 升级后立即享受新等级的所有功能
        </Text>
        <Text style={commonStyles.textSecondary}>
          • 如有问题请联系客服支持
        </Text>
      </Card>
    </Screen>
  );
};

// 样式已移至模块化组件中，无需在此定义
