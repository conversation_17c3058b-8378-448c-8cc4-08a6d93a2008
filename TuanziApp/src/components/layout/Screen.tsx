/**
 * 通用屏幕布局组件
 * 
 * 功能：
 * - 提供统一的屏幕布局
 * - 支持滚动和非滚动模式
 * - 可配置的内边距
 * - 安全区域处理
 */

import React from 'react';
import { 
  View, 
  ScrollView, 
  SafeAreaView, 
  StyleSheet, 
  ViewStyle 
} from 'react-native';
import { theme } from '../../styles/theme';

interface ScreenProps {
  children: React.ReactNode;
  style?: ViewStyle;
  scrollable?: boolean;
  padding?: number;
  backgroundColor?: string;
  safeArea?: boolean;
}

/**
 * 通用屏幕布局组件
 */
export const Screen: React.FC<ScreenProps> = ({
  children,
  style,
  scrollable = true,
  padding = theme.spacing.xl,
  backgroundColor = theme.colors.background,
  safeArea = true,
}) => {
  const containerStyle: ViewStyle = {
    flex: 1,
    backgroundColor,
    padding,
    ...style,
  };

  const content = scrollable ? (
    <ScrollView 
      style={containerStyle}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      {children}
    </ScrollView>
  ) : (
    <View style={containerStyle}>
      {children}
    </View>
  );

  if (safeArea) {
    return (
      <SafeAreaView style={styles.safeArea}>
        {content}
      </SafeAreaView>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
});
