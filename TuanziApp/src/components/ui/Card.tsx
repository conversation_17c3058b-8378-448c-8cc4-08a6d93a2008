/**
 * 通用卡片组件
 * 
 * 功能：
 * - 提供统一的卡片样式
 * - 支持阴影、圆角、边框等自定义
 * - 可配置的内边距和外边距
 * - 支持点击事件
 */

import React from 'react';
import { 
  View, 
  StyleSheet, 
  TouchableOpacity, 
  ViewStyle, 
  TouchableOpacityProps 
} from 'react-native';

interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
  borderRadius?: number;
  backgroundColor?: string;
  shadowEnabled?: boolean;
  borderColor?: string;
  borderWidth?: number;
  onPress?: () => void;
}

/**
 * 通用卡片组件
 */
export const Card: React.FC<CardProps> = ({
  children,
  style,
  padding = 16,
  margin = 0,
  borderRadius = 12,
  backgroundColor = '#ffffff',
  shadowEnabled = true,
  borderColor,
  borderWidth = 0,
  onPress,
  ...props
}) => {
  const cardStyle: ViewStyle = {
    backgroundColor,
    borderRadius,
    padding,
    margin,
    borderColor,
    borderWidth,
    ...(shadowEnabled && styles.shadow),
    ...style,
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
