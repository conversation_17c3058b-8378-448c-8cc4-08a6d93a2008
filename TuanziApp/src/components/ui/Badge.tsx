/**
 * 通用徽章组件
 * 
 * 功能：
 * - 多种徽章变体（success, warning, error, info等）
 * - 可配置的尺寸
 * - 支持自定义颜色
 * - 支持图标
 */

import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ViewStyle, 
  TextStyle 
} from 'react-native';

type BadgeVariant = 'success' | 'warning' | 'error' | 'info' | 'neutral';
type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeProps {
  text: string;
  variant?: BadgeVariant;
  size?: BadgeSize;
  style?: ViewStyle;
  textStyle?: TextStyle;
  backgroundColor?: string;
  textColor?: string;
  icon?: React.ReactNode;
}

/**
 * 通用徽章组件
 */
export const Badge: React.FC<BadgeProps> = ({
  text,
  variant = 'neutral',
  size = 'medium',
  style,
  textStyle,
  backgroundColor,
  textColor,
  icon,
}) => {
  const badgeStyle = [
    styles.badge,
    styles[`${variant}Badge`],
    styles[`${size}Badge`],
    backgroundColor && { backgroundColor },
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    textColor && { color: textColor },
    textStyle,
  ];

  return (
    <View style={badgeStyle}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={textStyleCombined}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 尺寸样式
  smallBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  mediumBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  largeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },

  // 变体样式
  successBadge: {
    backgroundColor: '#D1FAE5',
  },
  warningBadge: {
    backgroundColor: '#FEF3C7',
  },
  errorBadge: {
    backgroundColor: '#FEE2E2',
  },
  infoBadge: {
    backgroundColor: '#DBEAFE',
  },
  neutralBadge: {
    backgroundColor: '#F3F4F6',
  },

  // 文本样式
  text: {
    fontWeight: '600',
  },

  // 文本尺寸
  smallText: {
    fontSize: 10,
  },
  mediumText: {
    fontSize: 12,
  },
  largeText: {
    fontSize: 14,
  },

  // 文本变体颜色
  successText: {
    color: '#065F46',
  },
  warningText: {
    color: '#92400E',
  },
  errorText: {
    color: '#991B1B',
  },
  infoText: {
    color: '#1E40AF',
  },
  neutralText: {
    color: '#374151',
  },

  // 图标容器
  iconContainer: {
    marginRight: 4,
  },
});
