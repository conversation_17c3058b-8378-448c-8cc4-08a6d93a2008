/**
 * 排版组件
 * 
 * 功能：
 * - 提供统一的文本样式
 * - 支持多种文本变体
 * - 基于主题系统的一致性设计
 */

import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { theme, text, combine } from '../../styles';

type TypographyVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'body1' | 'body2' | 'caption' | 'overline';
type TypographyColor = keyof typeof theme.colors;

interface TypographyProps extends TextProps {
  variant?: TypographyVariant;
  color?: TypographyColor;
  align?: 'left' | 'center' | 'right' | 'justify';
  children: React.ReactNode;
}

/**
 * 排版组件
 */
export const Typography: React.FC<TypographyProps> = ({
  variant = 'body1',
  color = 'textPrimary',
  align = 'left',
  style,
  children,
  ...props
}) => {
  const variantStyle = styles[variant];
  const colorStyle = text.color(color);
  const alignStyle = text.align(align);

  const combinedStyle = combine(variantStyle, colorStyle, alignStyle, style);

  return (
    <Text style={combinedStyle} {...props}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  h1: {
    fontSize: theme.typography.fontSize['5xl'],
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: theme.typography.fontSize['5xl'] * theme.typography.lineHeight.tight,
  },
  h2: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: theme.typography.fontSize['4xl'] * theme.typography.lineHeight.tight,
  },
  h3: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    lineHeight: theme.typography.fontSize['3xl'] * theme.typography.lineHeight.tight,
  },
  h4: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    lineHeight: theme.typography.fontSize['2xl'] * theme.typography.lineHeight.normal,
  },
  body1: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.lg * theme.typography.lineHeight.normal,
  },
  body2: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  caption: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.normal,
  },
  overline: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
});
