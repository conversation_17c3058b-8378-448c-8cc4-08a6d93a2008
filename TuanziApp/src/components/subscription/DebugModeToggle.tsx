/**
 * 调试模式切换组件
 * 
 * 功能：
 * - 开发环境下显示调试模式开关
 * - 提供清晰的说明文字
 * - 美观的开关样式
 */

import React from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';
import { Card } from '../ui';

interface DebugModeToggleProps {
  isDebugMode: boolean;
  onToggle: (value: boolean) => void;
}

/**
 * 调试模式切换组件
 */
export const DebugModeToggle: React.FC<DebugModeToggleProps> = ({
  isDebugMode,
  onToggle,
}) => {
  // 只在开发环境下显示
  if (!__DEV__) {
    return null;
  }

  return (
    <Card 
      style={styles.debugCard}
      backgroundColor="#fff3cd"
      borderWidth={1}
      borderColor="#ffeaa7"
    >
      <View style={styles.debugHeader}>
        <Text style={styles.debugTitle}>开发调试模式</Text>
        <Switch
          value={isDebugMode}
          onValueChange={onToggle}
          trackColor={{ false: '#767577', true: '#81b0ff' }}
          thumbColor={isDebugMode ? '#f5dd4b' : '#f4f3f4'}
        />
      </View>
      <Text style={styles.debugDescription}>
        开启后可以直接切换订阅等级进行功能测试
      </Text>
    </Card>
  );
};

const styles = StyleSheet.create({
  debugCard: {
    marginBottom: 20,
  },
  debugHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#856404',
  },
  debugDescription: {
    fontSize: 14,
    color: '#856404',
  },
});
