/**
 * 订阅状态卡片组件
 * 
 * 功能：
 * - 显示当前订阅状态
 * - 突出显示当前计划信息
 * - 美观的状态展示
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from '../ui';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionPlan {
  level: SubscriptionLevel;
  name: string;
  price: number;
  priceText: string;
  features: string[];
  roomLimit: string;
  timeLimit: string;
  color: string;
}

interface SubscriptionStatusCardProps {
  currentPlan: SubscriptionPlan;
}

/**
 * 订阅状态卡片组件
 */
export const SubscriptionStatusCard: React.FC<SubscriptionStatusCardProps> = ({
  currentPlan,
}) => {
  return (
    <Card style={styles.statusCard}>
      <Text style={styles.statusTitle}>当前订阅</Text>
      <Text style={[styles.statusLevel, { color: currentPlan.color }]}>
        {currentPlan.name}
      </Text>
      <Text style={styles.statusPrice}>{currentPlan.priceText}</Text>
      
      {/* 快速信息 */}
      <View style={styles.quickInfoContainer}>
        <View style={styles.quickInfoItem}>
          <Text style={styles.quickInfoLabel}>房间限制</Text>
          <Text style={styles.quickInfoValue}>{currentPlan.roomLimit}</Text>
        </View>
        <View style={styles.quickInfoDivider} />
        <View style={styles.quickInfoItem}>
          <Text style={styles.quickInfoLabel}>时长限制</Text>
          <Text style={styles.quickInfoValue}>{currentPlan.timeLimit}</Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  statusCard: {
    alignItems: 'center',
    marginBottom: 20,
  },
  statusTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  statusLevel: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusPrice: {
    fontSize: 16,
    color: '#888',
    marginBottom: 16,
  },
  quickInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    width: '100%',
  },
  quickInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickInfoLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  quickInfoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  quickInfoDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 16,
  },
});
