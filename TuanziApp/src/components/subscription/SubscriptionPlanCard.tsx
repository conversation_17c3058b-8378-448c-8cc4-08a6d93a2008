/**
 * 订阅计划卡片组件
 * 
 * 功能：
 * - 显示订阅计划详细信息
 * - 支持当前计划标识
 * - 升级/降级按钮
 * - 调试模式支持
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, Button, Badge } from '../ui';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionPlan {
  level: SubscriptionLevel;
  name: string;
  price: number;
  priceText: string;
  features: string[];
  roomLimit: string;
  timeLimit: string;
  color: string;
}

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isCurrentPlan: boolean;
  isUpgrade: boolean;
  isDebugMode: boolean;
  onUpgrade: (level: SubscriptionLevel) => void;
  onDebugChange: (level: SubscriptionLevel) => void;
}

/**
 * 订阅计划卡片组件
 */
export const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({
  plan,
  isCurrentPlan,
  isUpgrade,
  isDebugMode,
  onUpgrade,
  onDebugChange,
}) => {
  return (
    <Card
      style={[
        styles.planCard,
        isCurrentPlan && styles.currentPlanCard
      ]}
      borderWidth={isCurrentPlan ? 2 : 0}
      borderColor={isCurrentPlan ? '#4CAF50' : undefined}
      padding={0}
    >
      {/* 计划头部 */}
      <View style={[styles.planHeader, { backgroundColor: plan.color }]}>
        <Text style={styles.planName}>{plan.name}</Text>
        <Text style={styles.planPrice}>{plan.priceText}</Text>
        {isCurrentPlan && (
          <View style={styles.currentBadgeContainer}>
            <Badge 
              text="当前" 
              variant="success" 
              size="small"
              backgroundColor="rgba(255, 255, 255, 0.2)"
              textColor="#ffffff"
            />
          </View>
        )}
      </View>

      {/* 计划内容 */}
      <View style={styles.planContent}>
        {/* 限制信息 */}
        <View style={styles.limitsSection}>
          <Text style={styles.limitText}>房间限制：{plan.roomLimit}</Text>
          <Text style={styles.limitText}>时长限制：{plan.timeLimit}</Text>
        </View>

        {/* 功能特性 */}
        <View style={styles.featuresSection}>
          <Text style={styles.featuresTitle}>功能特性：</Text>
          {plan.features.map((feature, index) => (
            <Text key={index} style={styles.featureText}>• {feature}</Text>
          ))}
        </View>

        {/* 操作按钮 */}
        <View style={styles.actionSection}>
          {!isCurrentPlan && (
            <Button
              title={isUpgrade ? '升级' : '降级'}
              onPress={() => onUpgrade(plan.level)}
              variant={isUpgrade ? 'success' : 'warning'}
              style={styles.actionButton}
            />
          )}
          
          {isDebugMode && (
            <Button
              title="调试切换"
              onPress={() => onDebugChange(plan.level)}
              variant="secondary"
              size="small"
              style={styles.debugButton}
            />
          )}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  planCard: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  currentPlanCard: {
    // 当前计划的特殊样式已通过props处理
  },
  planHeader: {
    padding: 20,
    alignItems: 'center',
    position: 'relative',
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  planPrice: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  currentBadgeContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  planContent: {
    padding: 20,
  },
  limitsSection: {
    marginBottom: 16,
  },
  limitText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  featuresSection: {
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  actionSection: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  debugButton: {
    flex: 1,
  },
});
