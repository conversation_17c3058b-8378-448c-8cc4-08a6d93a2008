import React, { useState, useRef, useMemo } from 'react';
import { View, StyleSheet, PanResponder } from 'react-native';
import Svg, { Path } from 'react-native-svg';

// PathData 现在只包含最终的SVG路径字符串和ID
export interface PathData {
  id: string;
  path: string;
  color: string;
}

// 用于在绘图过程中存储点坐标的接口
interface Point {
  x: number;
  y: number;
}

// Props interface for the PictionaryCanvas component
interface PictionaryCanvasProps {
  isDrawer: boolean;
  onDraw: (pathData: PathData) => void;
  paths: PathData[];
}

// --- 核心优化：平滑算法辅助函数 ---
const createSvgPath = (points: Point[]): string => {
    if (points.length < 2) {
        return points.length === 1 ? `M${points[0].x},${points[0].y}` : '';
    }

    let path = `M${points[0].x},${points[0].y}`;
    let p1 = points[0];
    let p2 = points[1];

    for (let i = 1; i < points.length; i++) {
        // 计算两个点之间的中点，作为二次Bézier曲线的终点
        const midPoint = {
            x: (p1.x + p2.x) / 2,
            y: (p1.y + p2.y) / 2,
        };
        // 使用前一个点作为控制点，中点作为终点，绘制平滑曲线
        path += ` Q${p1.x},${p1.y} ${midPoint.x},${midPoint.y}`;
        p1 = points[i];
        p2 = points[i + 1];
    }
    // 连接最后一个点
    path += ` L${p1.x},${p1.y}`;
    return path;
};


export const PictionaryCanvas: React.FC<PictionaryCanvasProps> = ({
  isDrawer,
  onDraw,
  paths,
}) => {
  // --- 更改：currentPath现在存储点数组，而不是SVG字符串 ---
  const [currentPoints, setCurrentPoints] = useState<Point[]>([]);
  const currentPathId = useRef<string | null>(null);
  
  const viewRef = useRef<View>(null);
  const canvasPosition = useRef<{pageX: number, pageY: number} | null>(null);

  const panResponder = useMemo(() => PanResponder.create({
    onStartShouldSetPanResponder: () => isDrawer,
    onMoveShouldSetPanResponder: () => isDrawer,

    onPanResponderGrant: (e, gestureState) => {
      canvasPosition.current = null;
      viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
        canvasPosition.current = { pageX, pageY };
        const adjustedX = Math.round(gestureState.x0 - pageX);
        const adjustedY = Math.round(gestureState.y0 - pageY);
        
        // --- 更改：开始一条新的路径 ---
        // 生成ID并开始记录点
        currentPathId.current = `path_${Date.now()}`;
        setCurrentPoints([{ x: adjustedX, y: adjustedY }]);
      });
    },

    onPanResponderMove: (e, gestureState) => {
      if (!canvasPosition.current) return;
      const adjustedX = Math.round(gestureState.moveX - canvasPosition.current.pageX);
      const adjustedY = Math.round(gestureState.moveY - canvasPosition.current.pageY);
      
      // --- 更改：持续添加新的点到数组中 ---
      setCurrentPoints(prevPoints => [...prevPoints, { x: adjustedX, y: adjustedY }]);
    },

    onPanResponderRelease: () => {
      if (currentPoints.length < 2 || !currentPathId.current) {
        // 如果只是点击，则不发送路径
        setCurrentPoints([]);
        return;
      }
      
      // --- 更改：在绘图结束后，一次性生成平滑的SVG路径 ---
      const finalSmoothPath = createSvgPath(currentPoints);
      
      onDraw({
        id: currentPathId.current,
        path: finalSmoothPath,
        color: 'black',
      });
      
      // 清理当前路径
      setCurrentPoints([]);
      currentPathId.current = null;
    },
  }), [isDrawer, onDraw, currentPoints]);

  // --- 更改：动态生成当前正在绘制的路径 ---
  const currentPathForRender = useMemo(() => {
    if (currentPoints.length === 0 || !currentPathId.current) return null;
    return {
      id: currentPathId.current,
      path: createSvgPath(currentPoints),
      color: 'black',
    };
  }, [currentPoints]);

  const allPaths = useMemo(() => {
    return currentPathForRender ? [...paths, currentPathForRender] : paths;
  }, [paths, currentPathForRender]);

  return (
    <View ref={viewRef} style={styles.container} {...panResponder.panHandlers}>
      <Svg width="100%" height="100%">
        {allPaths.map(pathData => (
          <Path
            key={pathData.id}
            d={pathData.path}
            stroke={pathData.color}
            strokeWidth={3}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        ))}
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
  },
});
