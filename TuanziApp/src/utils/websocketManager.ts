// TuanziApp/src/utils/websocketManager.ts

import { authManager } from './authManager';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface WebSocketMessage {
  type: string;
  payload: any;
}

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private roomCode: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000; // 1秒
  private heartbeatInterval?: NodeJS.Timeout;
  private onMessage?: (message: WebSocketMessage) => void;
  private onConnectionChange?: (connected: boolean) => void;
  
  /**
   * 连接到房间WebSocket
   */
  async connect(roomCode: string): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem('access_token');
      if (!token) {
        console.error('No access token found');
        authManager.handleAuthResponse({
          error: 'No token found',
          code: 'AUTH_REQUIRED',
          redirect_to_login: true
        });
        return false;
      }
      
      this.roomCode = roomCode;
      const wsUrl = `ws://localhost:8000/ws/room/${roomCode}/?token=${token}`;
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log(`WebSocket connected to room ${roomCode}`);
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.onConnectionChange?.(true);
      };
      
      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };
      
      this.ws.onclose = (event) => {
        console.log(`WebSocket closed with code ${event.code}`);
        this.stopHeartbeat();
        this.onConnectionChange?.(false);
        
        // 处理不同的关闭代码
        switch (event.code) {
          case 4001: // 认证失败
            authManager.handleAuthResponse({
              error: 'WebSocket authentication failed',
              code: 'AUTH_REQUIRED',
              redirect_to_login: true
            });
            break;
          case 4005: // 会话超时
            authManager.handleAuthResponse({
              error: 'Session expired due to inactivity',
              code: 'SESSION_TIMEOUT',
              redirect_to_login: true,
              timeout_minutes: 30
            });
            break;
          case 1000: // 正常关闭
            // 不需要重连
            break;
          default:
            // 其他错误，尝试重连
            this.attemptReconnect();
        }
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
      
      return true;
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      return false;
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close(1000); // 正常关闭
      this.ws = null;
    }
    this.roomCode = null;
    this.reconnectAttempts = 0;
  }
  
  /**
   * 发送消息
   */
  send(action: string, payload: any = {}) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        action,
        payload
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }
  
  /**
   * 设置消息处理器
   */
  setOnMessage(handler: (message: WebSocketMessage) => void) {
    this.onMessage = handler;
  }
  
  /**
   * 设置连接状态变化处理器
   */
  setOnConnectionChange(handler: (connected: boolean) => void) {
    this.onConnectionChange = handler;
  }
  
  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'session_timeout':
        // 处理会话超时
        authManager.handleAuthResponse({
          error: message.payload.message,
          code: 'SESSION_TIMEOUT',
          redirect_to_login: message.payload.redirect_to_login,
          timeout_minutes: message.payload.timeout_minutes
        });
        break;
      case 'heartbeat_response':
        // 心跳响应，不需要特殊处理
        break;
      default:
        // 传递给外部处理器
        this.onMessage?.(message);
    }
  }
  
  /**
   * 尝试重连
   */
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnect attempts reached');
      return;
    }
    
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      if (this.roomCode) {
        this.connect(this.roomCode);
      }
    }, delay);
  }
  
  /**
   * 开始心跳
   */
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.send('heartbeat');
    }, 30000); // 每30秒发送一次心跳
  }
  
  /**
   * 停止心跳
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }
  
  /**
   * 获取连接状态
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// 导出单例实例
export const websocketManager = new WebSocketManager();
