现在有这样一些逻辑性bug：
1. 首先是有关每个用户对于模板的访问权限的问题，我发现在创建新房间中每个用户能看到自己创建的模板，但是看不到每个人都有的默认模板，而在日历预约中能看到所有events eventtemplate数据表中的模板，这绝对是不正常的。理想情况下每个用户应当能看到系统默认的模板，并且仅能看到自己创建的模板，也就是creator_id一栏与用户id一样的条目

针对这个问题，我认为应该修改或者增加模型中的函数，使得所有不管是直接创建房间还是预约创建房间都能从同一个接口获得可以创建的模板列表；并且你没有遵循我之前的设计，应该创建一个独立的数据表用于储存系统默认的模板，与用户创建的模板也就是目前的eventtemplate表格进行区分。

2. 
我不确定你是怎么做的，但是我必须确认一个事情，那就是有关预约系统与房间状态监测系统的刷新。我理想中的状态时这样的：

首先我们创建一个房间，它在SCHEDULED状态，等待对应时间的到来。

在预订时间到了之后，房间回收机制不做任何响应，而且房间状态仍然是SCHEDULED，但是此时可以允许人员加入

人员加入后，房间状态变为WAITING也就是等待房主开始游戏。但是此处处理玩家身份需要额外注意，如果加入的人是预约这个活动的人，那么他就是房主。但是如果加入这个房间的人不是预约者，那么他的身份应当首先是一个普通的参与者，然后等待5分钟是否有预约者加入。如果预约者加入了，预约者仍然是房主。如果预约者没有加入，就需要按顺序将时间上最开始加入房间的人设为房主。

剩下的逻辑就是一个正常房间的逻辑

等到预订时间经过之后，房间回收机制会处理仍然处于SCHEDULED状态的房间为CLOSED，因为它没有人参与而且时间已过。

