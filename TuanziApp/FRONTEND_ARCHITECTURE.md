# 前端架构文档

## 概述

本项目采用模块化的前端架构设计，提升代码的可维护性、可扩展性和可复用性。

## 架构特点

### 1. 模块化组件系统
- **UI组件库**: 通用的基础组件（Button, Card, Badge, Typography等）
- **业务组件**: 特定功能的组件（SubscriptionPlanCard, SubscriptionStatusCard等）
- **布局组件**: 页面布局相关组件（Screen等）

### 2. 统一的主题系统
- **颜色系统**: 主色调、辅助色、状态色、中性色
- **字体系统**: 字体大小、权重、行高
- **间距系统**: 统一的间距规范
- **阴影系统**: 一致的阴影效果

### 3. 样式工具函数
- **间距工具**: `spacing.m()`, `spacing.p()` 等
- **文本工具**: `text.size()`, `text.color()` 等
- **布局工具**: `layout.flex()`, `layout.center()` 等
- **组合工具**: `combine()`, `when()` 等

## 目录结构

```
src/
├── components/           # 组件库
│   ├── ui/              # 通用UI组件
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Badge.tsx
│   │   ├── Typography.tsx
│   │   └── index.ts
│   ├── layout/          # 布局组件
│   │   ├── Screen.tsx
│   │   └── index.ts
│   ├── subscription/    # 订阅相关组件
│   │   ├── SubscriptionPlanCard.tsx
│   │   ├── SubscriptionStatusCard.tsx
│   │   ├── DebugModeToggle.tsx
│   │   └── index.ts
│   └── index.ts         # 统一导出
├── styles/              # 样式系统
│   ├── theme.ts         # 主题定义
│   ├── commonStyles.ts  # 通用样式
│   ├── utils.ts         # 样式工具函数
│   └── index.ts         # 统一导出
└── screens/             # 页面组件
    └── SubscriptionScreen.tsx
```

## 使用示例

### 1. 使用UI组件

```tsx
import { Button, Card, Typography } from '../components';

// 按钮组件
<Button
  title="升级"
  variant="primary"
  size="medium"
  onPress={handleUpgrade}
/>

// 卡片组件
<Card padding={20} shadowEnabled>
  <Typography variant="h3">标题</Typography>
  <Typography variant="body1" color="textSecondary">
    内容文本
  </Typography>
</Card>
```

### 2. 使用样式工具

```tsx
import { spacing, text, combine } from '../styles';

const styles = StyleSheet.create({
  container: combine(
    spacing.p('lg'),
    spacing.m('md'),
    background.color('surface')
  ),
  title: combine(
    text.size('2xl'),
    text.weight('bold'),
    text.color('textPrimary')
  ),
});
```

### 3. 使用主题系统

```tsx
import { theme } from '../styles';

const customStyle = {
  backgroundColor: theme.colors.primary,
  padding: theme.spacing.lg,
  borderRadius: theme.borderRadius.md,
  ...theme.shadows.md,
};
```

## 组件设计原则

### 1. 单一职责
每个组件只负责一个特定的功能，保持简单和专注。

### 2. 可配置性
通过props提供丰富的配置选项，满足不同使用场景。

### 3. 一致性
所有组件都基于统一的主题系统，确保视觉一致性。

### 4. 可复用性
组件设计考虑复用性，避免硬编码特定业务逻辑。

### 5. 类型安全
使用TypeScript提供完整的类型定义，提升开发体验。

## 扩展指南

### 添加新的UI组件

1. 在 `src/components/ui/` 目录下创建新组件
2. 遵循现有的设计模式和接口规范
3. 使用主题系统中的设计token
4. 在 `src/components/ui/index.ts` 中导出

### 添加新的主题token

1. 在 `src/styles/theme.ts` 中添加新的设计token
2. 更新相关的样式工具函数
3. 确保向后兼容性

### 创建业务组件

1. 在 `src/components/` 下创建业务相关的目录
2. 组合使用基础UI组件
3. 保持组件的独立性和可测试性

## 最佳实践

1. **优先使用组件库**: 尽量使用现有的UI组件，避免重复造轮子
2. **遵循主题系统**: 使用主题中定义的颜色、间距等，而不是硬编码值
3. **保持组件简洁**: 单个组件文件不超过300行，复杂组件考虑拆分
4. **编写清晰的接口**: 为组件props提供完整的TypeScript类型定义
5. **添加适当的文档**: 为复杂组件添加使用说明和示例

## 性能优化

1. **按需导入**: 使用具体的组件导入，避免导入整个组件库
2. **样式缓存**: 使用StyleSheet.create缓存样式对象
3. **条件渲染**: 合理使用条件渲染减少不必要的组件渲染
4. **memo优化**: 对纯展示组件使用React.memo进行优化

## 测试策略

1. **单元测试**: 为每个UI组件编写单元测试
2. **快照测试**: 使用快照测试确保组件渲染的一致性
3. **集成测试**: 测试组件之间的交互和数据流
4. **视觉回归测试**: 确保样式变更不会破坏现有设计
