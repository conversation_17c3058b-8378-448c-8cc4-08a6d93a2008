# 日历预约系统

## 概述

本系统为Tuanzi项目新增了完整的日历预约功能，允许用户预约未来某个时间的游戏房间。系统采用解耦设计，与现有的实时房间管理系统无缝集成。

## 核心功能

### 1. 房间预约
- 用户可以选择未来的时间点预约游戏房间
- 支持指定房间名称、游戏类型、开始时间和持续时长
- 预约房间状态为 `SCHEDULED`，不会影响现有的实时房间逻辑

### 2. 日历展示
- 可视化的月历界面，展示所有预约事件
- 支持月份切换和日期选择
- 显示每日预约数量和详细信息

### 3. 自动激活
- 定时任务自动检测到期的预约房间
- 将 `SCHEDULED` 状态的房间转换为 `WAITING` 状态
- 无缝接入现有的房间生命周期管理

## 技术架构

### 后端架构

#### 数据库模型扩展
- **Room模型**: 新增 `SCHEDULED` 状态和 `scheduled_start_time` 字段
- **状态机**: 扩展房间状态转换规则，支持预约状态
- **生命周期**: 预约房间的过期时间基于预约开始时间计算

#### API端点
- `POST /api/rooms/schedule/` - 创建预约房间
- `GET /api/calendar/reservations/` - 获取日历数据

#### 定时任务
- `activate_scheduled_rooms` 管理命令
- 支持单次执行和持续运行模式
- 自动激活到期的预约房间

### 前端架构

#### 新增屏幕
- **CalendarScreen**: 日历展示界面
- **ScheduleRoomScreen**: 预约房间表单

#### API集成
- **calendarApi.ts**: 日历和预约相关的API客户端
- 支持日期范围查询和预约创建

#### 导航集成
- 在主页添加"日历预约"按钮
- 完整的导航流程：主页 → 日历 → 预约表单

## 使用指南

### 用户操作流程

1. **查看日历**
   - 在主页点击"日历预约"按钮
   - 浏览月历，查看现有预约
   - 点击空白日期创建新预约

2. **创建预约**
   - 填写房间名称
   - 选择游戏模板
   - 设置预约时间和持续时长
   - 确认预约信息并提交

3. **预约激活**
   - 系统自动在预约时间到达时激活房间
   - 房间状态从"已预约"变为"等待中"
   - 用户可以正常加入和使用房间

### 管理员操作

#### 启动定时任务
```bash
# 单次检查
python manage.py activate_scheduled_rooms

# 持续运行模式
python manage.py activate_scheduled_rooms --continuous

# 仅查看将要激活的房间
python manage.py activate_scheduled_rooms --dry-run
```

#### 查看预约数据
```bash
# 查看所有预约房间
python manage.py shell
>>> from core.models import Room, RoomState
>>> Room.objects.filter(status=RoomState.SCHEDULED)
```

## 系统集成

### 与现有系统的兼容性

1. **房间管理器**: 扩展了状态转换规则，支持预约状态
2. **生命周期管理**: 预约房间激活后完全遵循现有的生命周期规则
3. **WebSocket通信**: 激活后的房间与普通房间无差异
4. **权限系统**: 完全兼容现有的订阅等级限制

### 数据库变更

```sql
-- 新增字段
ALTER TABLE core_room ADD COLUMN scheduled_start_time DATETIME NULL;

-- 新增状态
-- Room.status 字段的choices已更新，包含 'SCHEDULED' 状态
```

## 测试验证

### 自动化测试
运行测试脚本验证系统功能：
```bash
python test_calendar_system.py
```

测试覆盖：
- ✅ 预约房间创建
- ✅ 房间状态激活
- ✅ 日历数据查询
- ✅ 房间生命周期

### 手动测试
1. 启动Django服务器
2. 在前端创建预约
3. 验证API响应
4. 测试定时任务激活

## 部署注意事项

### 生产环境配置

1. **定时任务设置**
   ```bash
   # 添加到crontab，每分钟检查一次
   * * * * * cd /path/to/project && python manage.py activate_scheduled_rooms
   ```

2. **数据库迁移**
   ```bash
   python manage.py migrate
   ```

3. **前端依赖**
   ```bash
   npm install @react-native-community/datetimepicker @react-native-picker/picker
   ```

### 监控建议

- 监控定时任务的执行日志
- 定期检查预约房间的激活情况
- 监控API响应时间和错误率

## 未来扩展

### 可能的功能增强

1. **预约提醒**: 在预约开始前发送通知
2. **预约修改**: 允许用户修改或取消预约
3. **重复预约**: 支持周期性预约
4. **预约冲突检测**: 防止时间冲突
5. **预约统计**: 提供预约使用情况分析

### 技术优化

1. **缓存优化**: 对日历数据进行缓存
2. **批量处理**: 优化大量预约的处理性能
3. **分布式锁**: 在多实例环境下确保激活的原子性

## 总结

日历预约系统成功实现了以下目标：

- ✅ **无损扩展**: 不破坏现有房间管理架构
- ✅ **解耦设计**: 预约系统与实时系统独立运行
- ✅ **完整功能**: 从预约创建到自动激活的完整流程
- ✅ **用户友好**: 直观的日历界面和预约表单
- ✅ **系统稳定**: 通过全面测试验证功能正确性

该系统为Tuanzi项目提供了强大的预约功能，同时保持了系统的稳定性和可维护性。
