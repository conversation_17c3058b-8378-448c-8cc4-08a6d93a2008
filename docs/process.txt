房间系统单向生命周期状态机说明：


1. 等待状态：
一个用户以host身份创建一个房间，在房间内具有host的一切操作权利。
生成一个由大写英文字母和数字组成的六位唯一房间号，作为其他用户加入房间的房间号
现阶段，允许其他用户以participant的身份加入房间
只有在所有人按下准备按钮之后，允许房主通过按钮进入活跃状态

2. 活跃状态：
现在开始执行房间内部定义的环节逻辑，如游戏（你画我猜，狼人杀等）、投票、自由讨论等
其它用户仍然可以加入，但是需要等待对应的允许加入的接口，如重复游戏、新环节等时机
如果在常规检测中发现，超过了额定房间时间，进入结束状态。

3. 结束状态：
现在开始，不允许新玩家进入，并且在对应时间点分别弹窗“还剩15分钟”，“还剩5分钟”
在额定房间结束时间15分钟以后，强制结束房间，进入关闭状态

4. 关闭状态：
仅保留房间信息，禁止除开发者外任何用户直接的修改和删除，仅允许查看


