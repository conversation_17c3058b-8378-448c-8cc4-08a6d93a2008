# 团建游戏房间管理系统

## 概述

这是一个极度稳定且易于维护的团建游戏房间管理系统，专为高并发、高可用的实时应用场景设计。系统采用严格的状态机模式，确保房间状态转换的可预测性和一致性。

## 核心设计原则

### 1. 健壮性与稳定性
- **无单点故障**: 实现房主异常掉线后的自动转移机制
- **防御性编程**: 所有外部输入都经过严格验证
- **原子操作**: 所有状态转换都是原子性的，防止并发请求导致的状态错乱
- **优雅异常处理**: 妥善处理网络波动、用户突然退出等异常情况

### 2. 灵活性与用户体验
- **房主控制权**: 最大化房主的控制权（强制开始、踢人、结束游戏）
- **无感知加入**: 新用户中途加入自动以"观战者"身份融入
- **平滑状态过渡**: 避免粗暴打断用户核心体验

### 3. 资源管理
- **无僵尸房间**: 自动清理和销毁机制，防止内存和资源泄露
- **生命周期管理**: 完整的房间生命周期监控和管理

## 状态机设计

### 房间状态枚举 (RoomState)
```python
WAITING = 'WAITING'     # 等待中
ACTIVE = 'ACTIVE'       # 活跃中  
REVIEW = 'REVIEW'       # 结算/复盘中
CLOSED = 'CLOSED'       # 已关闭
```

### 用户状态枚举 (UserState)
```python
JOINED = 'JOINED'           # 已加入
READY = 'READY'             # 准备就绪
PLAYING = 'PLAYING'         # 游戏中
SPECTATING = 'SPECTATING'   # 观战中
```

### 状态转换规则
- **WAITING** → **ACTIVE**: 房主开始游戏
- **WAITING** → **CLOSED**: 房间过期或房主解散
- **ACTIVE** → **REVIEW**: 房主结束游戏
- **ACTIVE** → **CLOSED**: 房间过期或空置
- **REVIEW** → **CLOSED**: 复盘超时或房主解散
- **CLOSED**: 终态，不可转换

## 核心组件

### 1. RoomManager (房间管理器)
**位置**: `core/services/room_manager.py`

**职责**:
- 房间状态的原子性管理
- 用户权限验证
- 房主转移机制
- 房间生命周期管理
- 并发控制和防竞态条件

**关键方法**:
```python
async def create_room(host, template_id, **kwargs) -> Room
async def join_room(room_code, user) -> Tuple[bool, str]
async def leave_room(room_code, user) -> Tuple[bool, str]
async def transition_room_state(room_code, new_state, user) -> Tuple[bool, str]
```

### 2. RoomLifecycleManager (生命周期管理器)
**位置**: `core/services/room_lifecycle.py`

**职责**:
- 房间过期检测和自动关闭
- 空房间检测和清理
- 复盘阶段超时处理
- 房主心跳检测和自动转移
- 僵尸房间清理

### 3. 异常处理体系
**位置**: `core/exceptions.py`

**专用异常类型**:
- `RoomNotFoundException`: 房间不存在
- `PermissionDeniedException`: 权限不足
- `RoomFullException`: 房间已满
- `RoomExpiredException`: 房间已过期
- `InvalidStateTransitionException`: 无效状态转换

### 4. 增强的WebSocket消费者
**位置**: `core/consumers.py`

**新增功能**:
- 房主心跳机制
- 自动房间加入/离开
- 状态转换权限控制
- 增强的错误处理

## API接口

### 房间创建
```http
POST /api/rooms/create/
Content-Type: application/json
Authorization: Bearer <token>

{
    "template_id": 1
}
```

### 房间加入
```http
POST /api/rooms/join/
Content-Type: application/json
Authorization: Bearer <token>

{
    "room_code": "ABC123"
}
```

## WebSocket通信

### 连接
```
ws://localhost:8000/ws/room/{room_code}/?token={jwt_token}
```

### 新增动作
- `set_ready`: 设置准备状态
- `kick_player`: 踢出玩家（仅房主）
- `force_start`: 强制开始游戏（仅房主）
- `end_game`: 结束游戏（仅房主）
- `heartbeat`: 心跳检测

### 示例消息
```json
{
    "action": "set_ready",
    "payload": {}
}

{
    "action": "force_start",
    "payload": {}
}

{
    "action": "kick_player",
    "payload": {
        "username": "player_to_kick"
    }
}
```

## 监控和维护

### 启动房间监控
```bash
python manage.py start_room_monitoring --interval 60 --verbose
```

### 手动清理房间
```bash
python manage.py cleanup_rooms --dry-run
python manage.py cleanup_rooms
```

### 数据库迁移
```bash
python manage.py makemigrations core
python manage.py migrate
```

## 测试

### 运行测试
```bash
python manage.py test core.tests.test_room_manager
```

### 集成测试
```bash
python test/test_integration.py
```

## 配置参数

### 房间管理器配置
```python
ROOM_CODE_LENGTH = 6                # 房间号长度
HOST_HEARTBEAT_TIMEOUT = 60         # 房主心跳超时（秒）
REVIEW_TIMEOUT = 15 * 60            # 复盘阶段超时（15分钟）
EMPTY_ROOM_TIMEOUT = 5 * 60         # 空房间超时（5分钟）
```

### 生命周期管理器配置
```python
cleanup_interval = 60               # 清理检查间隔（秒）
```

## 部署注意事项

1. **数据库**: 确保数据库支持事务和行级锁
2. **Redis**: 建议使用Redis作为Channel Layer后端
3. **监控**: 启用详细日志记录和监控
4. **负载均衡**: 确保WebSocket连接的粘性会话

## 故障排除

### 常见问题
1. **房间状态不一致**: 检查数据库事务配置
2. **心跳超时**: 调整心跳间隔和超时设置
3. **内存泄露**: 确保监控服务正常运行

### 日志级别
```python
LOGGING = {
    'loggers': {
        'core.services': {
            'level': 'DEBUG',
        },
        'core.consumers': {
            'level': 'INFO',
        },
    }
}
```
