# Tuan<PERSON>_Backend/asgi.py

import os
import django
from channels.routing import Protocol<PERSON><PERSON><PERSON><PERSON><PERSON>, URLRouter
from django.core.asgi import get_asgi_application


# Explicitly set up Django before importing anything else
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

# Now that Django is set up, we can safely import other components
from core.middleware.session_management import WebSocketSessionMiddleware
from core.middleware.token_management import TokenAuthMiddleware
import core.routing

# This is the enhanced middleware stack with session management.
application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": WebSocketSessionMiddleware(
        TokenAuthMiddleware(
            URLRouter(
                core.routing.websocket_urlpatterns
            )
        )
    ),
})
