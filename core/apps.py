import asyncio
import logging
from django.apps import AppConfig
from django.conf import settings

logger = logging.getLogger(__name__)

class CoreConfig(AppConfig):
    """Core应用配置"""

    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    verbose_name = '核心模块'

    def ready(self):
        """应用准备就绪时的初始化"""
        # 导入信号处理器
        import core.signals

        # 只在主进程中启动监控服务
        if not settings.DEBUG or self._is_main_process():
            self._start_background_services()

    def _is_main_process(self):
        """检查是否为主进程"""
        import os
        return os.environ.get('RUN_MAIN') == 'true'

    def _start_background_services(self):
        """启动后台服务"""
        try:
            # 导入房间生命周期管理器
            from .services.room_lifecycle import lifecycle_manager

            # 在新的事件循环中启动监控
            def start_monitoring():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(lifecycle_manager.start_monitoring())
                    loop.run_forever()
                except Exception as e:
                    logger.error(f"启动房间监控服务失败: {e}", exc_info=True)
                finally:
                    loop.close()

            # 在后台线程中启动监控
            import threading
            monitoring_thread = threading.Thread(
                target=start_monitoring,
                daemon=True,
                name='RoomLifecycleMonitor'
            )
            monitoring_thread.start()

            logger.info("房间生命周期监控服务已启动")

        except Exception as e:
            logger.error(f"启动后台服务失败: {e}", exc_info=True)
