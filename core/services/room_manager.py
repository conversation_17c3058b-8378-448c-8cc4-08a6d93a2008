# core/services/room_manager.py

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from channels.db import database_sync_to_async

from ..models import Room, User, RoomParticipant, RoomState, UserState
from ..exceptions import (
    RoomNotFoundException, 
    PermissionDeniedException,
    RoomFullException,
    RoomExpiredException,
    InvalidStateTransitionException
)

logger = logging.getLogger(__name__)

class RoomManager:
    """
    中心化的房间管理服务
    
    职责：
    1. 房间状态的原子性管理
    2. 用户权限验证
    3. 房主转移机制
    4. 房间生命周期管理
    5. 并发控制和防竞态条件
    """
    
    # 房间状态转换规则矩阵 - 完整的状态机
    VALID_STATE_TRANSITIONS = {
        RoomState.SCHEDULED: [RoomState.OPEN, RoomState.CLOSED],                    # 预约房间可以开启或关闭
        RoomState.OPEN: [RoomState.WAITING_FOR_HOST, RoomState.READY, RoomState.CLOSED],  # 开启房间等待玩家
        RoomState.WAITING_FOR_HOST: [RoomState.READY, RoomState.OPEN, RoomState.CLOSED],  # 等待房主确定
        RoomState.READY: [RoomState.IN_PROGRESS, RoomState.OPEN, RoomState.CLOSED],       # 准备就绪等待开始
        RoomState.IN_PROGRESS: [RoomState.ENDED, RoomState.CLOSED],                       # 活动进行中
        RoomState.ENDED: [RoomState.CLOSED],                                              # 活动结束
        RoomState.CLOSED: [],                                                             # 终态，不可转换

        # 向后兼容的别名
        # RoomState.OPEN: [RoomState.IN_PROGRESS, RoomState.CLOSED],  # 兼容旧代码
        # RoomState.IN_PROGRESS: [RoomState.ENDED, RoomState.CLOSED],         # 兼容旧代码
        # RoomState.ENDED: [RoomState.CLOSED],                          # 兼容旧代码
    }
    
    # 房间配置常量
    ROOM_CODE_LENGTH = getattr(settings, 'ROOM_CODE_LENGTH', 6)
    HOST_HEARTBEAT_TIMEOUT = getattr(settings, 'HOST_HEARTBEAT_TIMEOUT', 30)
    REVIEW_TIMEOUT = getattr(settings, 'REVIEW_TIMEOUT', 15)
    EMPTY_ROOM_TIMEOUT = getattr(settings, 'EMPTY_ROOM_TIMEOUT', 5)
    
    def __init__(self):
        self._room_locks: Dict[str, asyncio.Lock] = {}
        self._host_heartbeats: Dict[str, datetime] = {}
    
    async def get_room_lock(self, room_code: str) -> asyncio.Lock:
        """获取房间专用锁，确保单个房间操作的原子性"""
        if room_code not in self._room_locks:
            self._room_locks[room_code] = asyncio.Lock()
        return self._room_locks[room_code]
    
    @database_sync_to_async
    def _create_room_sync(self, host: User, template_id: int, **kwargs) -> Room:
        """同步创建房间的数据库操作"""
        with transaction.atomic():
            # 生成唯一房间号
            while True:
                room_code = uuid.uuid4().hex.upper()[:self.ROOM_CODE_LENGTH]
                if not Room.objects.filter(room_code=room_code).exists():
                    break
            
            # 创建房间
            room = Room.objects.create(
                room_code=room_code,
                host=host,
                event_template_id=template_id,
                status=RoomState.OPEN,
                **kwargs
            )
            
            # 设置订阅限制
            room.set_limits_by_subscription(host)
            room.save()
            
            # 将房主添加为参与者
            RoomParticipant.objects.create(
                room=room,
                user=host,
                role=RoomParticipant.ROLE_HOST,
                state=UserState.JOINED
            )
            
            return room
    
    def create_room_sync(self, host: User, template_id: int, **kwargs) -> Room:
        """
        同步创建新房间 - 用于Django视图

        Args:
            host: 房主用户
            template_id: 事件模板ID
            **kwargs: 其他房间参数

        Returns:
            Room: 创建的房间对象

        Raises:
            ValidationError: 参数验证失败
        """
        try:
            with transaction.atomic():
                # 生成唯一房间号
                while True:
                    room_code = uuid.uuid4().hex.upper()[:self.ROOM_CODE_LENGTH]
                    if not Room.objects.filter(room_code=room_code).exists():
                        break

                # 创建房间
                room = Room.objects.create(
                    room_code=room_code,
                    host=host,
                    event_template_id=template_id,
                    status=RoomState.OPEN,
                    **kwargs
                )

                # 设置订阅限制
                room.set_limits_by_subscription(host)
                room.save()

                # 将房主添加为参与者
                RoomParticipant.objects.create(
                    room=room,
                    user=host,
                    role=RoomParticipant.ROLE_HOST,
                    state=UserState.JOINED
                )

                # 初始化房主心跳
                self._host_heartbeats[room.room_code] = timezone.now()

                logger.info(f"房间 {room.room_code} 创建成功，房主: {host.username}")
                return room

        except Exception as e:
            logger.error(f"创建房间失败: {e}", exc_info=True)
            raise ValidationError(f"创建房间失败: {e}")

    async def create_room(self, host: User, template_id: int, **kwargs) -> Room:
        """
        异步创建新房间 - 用于WebSocket

        Args:
            host: 房主用户
            template_id: 事件模板ID
            **kwargs: 其他房间参数

        Returns:
            Room: 创建的房间对象

        Raises:
            ValidationError: 参数验证失败
        """
        try:
            room = await self._create_room_sync(host, template_id, **kwargs)

            # 初始化房主心跳
            self._host_heartbeats[room.room_code] = timezone.now()

            logger.info(f"房间 {room.room_code} 创建成功，房主: {host.username}")
            return room

        except Exception as e:
            logger.error(f"创建房间失败: {e}", exc_info=True)
            raise ValidationError(f"创建房间失败: {e}")
    
    @database_sync_to_async
    def _get_room_with_relations(self, room_code: str) -> Optional[Room]:
        """获取房间及其关联数据"""
        try:
            return Room.objects.select_related('host', 'event_template').get(
                room_code=room_code
            )
        except Room.DoesNotExist:
            return None
    
    def get_room_sync(self, room_code: str) -> Room:
        """
        同步获取房间对象 - 用于Django视图

        Args:
            room_code: 房间代码

        Returns:
            Room: 房间对象

        Raises:
            RoomNotFoundException: 房间不存在
        """
        try:
            return Room.objects.select_related('host', 'event_template').get(
                room_code=room_code
            )
        except Room.DoesNotExist:
            raise RoomNotFoundException(f"房间 {room_code} 不存在")

    async def get_room(self, room_code: str) -> Room:
        """
        异步获取房间对象 - 用于WebSocket

        Args:
            room_code: 房间代码

        Returns:
            Room: 房间对象

        Raises:
            RoomNotFoundException: 房间不存在
        """
        room = await self._get_room_with_relations(room_code)
        if not room:
            raise RoomNotFoundException(f"房间 {room_code} 不存在")
        return room
    
    def validate_state_transition(self, room: Room, new_state: RoomState) -> bool:
        """
        验证状态转换是否合法

        Args:
            room: 房间对象
            new_state: 目标状态

        Returns:
            bool: 是否可以转换
        """
        current_state = RoomState(room.status)
        valid_transitions = self.VALID_STATE_TRANSITIONS.get(current_state, [])
        return new_state in valid_transitions
    
    @database_sync_to_async
    def _transition_room_state_sync(self, room_code: str, new_state: RoomState, 
                                   user: Optional[User] = None) -> Tuple[bool, str]:
        """同步执行房间状态转换"""
        try:
            with transaction.atomic():
                room = Room.objects.select_for_update().get(room_code=room_code)
                
                # 验证状态转换
                if not self.validate_state_transition(room, new_state):
                    return False, f"无法从 {room.status} 转换到 {new_state}"
                
                # 权限检查（如果指定了用户）
                if user and room.host_id != user.id:
                    return False, "只有房主可以执行此操作"
                
                # 执行状态转换
                old_state = room.status
                room.status = new_state
                
                # 根据新状态设置相关字段
                if new_state == RoomState.ENDED:
                    room.review_started_at = timezone.now()
                elif new_state == RoomState.CLOSED:
                    room.closed_at = timezone.now()
                
                room.save()
                
                logger.info(f"房间 {room_code} 状态从 {old_state} 转换到 {new_state}")
                return True, "状态转换成功"
                
        except Room.DoesNotExist:
            return False, "房间不存在"
        except Exception as e:
            logger.error(f"状态转换失败: {e}", exc_info=True)
            return False, f"状态转换失败: {e}"
    
    async def transition_room_state(self, room_code: str, new_state: RoomState,
                                   user: Optional[User] = None) -> Tuple[bool, str]:
        """
        安全地转换房间状态

        Args:
            room_code: 房间代码
            new_state: 目标状态
            user: 执行操作的用户（用于权限检查）

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        async with await self.get_room_lock(room_code):
            return await self._transition_room_state_sync(room_code, new_state, user)

    def transition_room_state_sync(self, room_code: str, new_state: RoomState,
                                  user: Optional[User] = None) -> Tuple[bool, str]:
        """
        同步版本的房间状态转换方法
        主要用于管理命令和其他同步上下文

        Args:
            room_code: 房间代码
            new_state: 目标状态
            user: 执行操作的用户（用于权限检查）

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            with transaction.atomic():
                room = Room.objects.select_for_update().get(room_code=room_code)

                # 验证状态转换
                if not self.validate_state_transition(room, new_state):
                    return False, f"无法从 {room.status} 转换到 {new_state}"

                # 权限检查（如果指定了用户）
                if user and room.host_id != user.id:
                    return False, "只有房主可以执行此操作"

                # 执行状态转换
                old_state = room.status
                room.status = new_state

                # 根据新状态设置相关字段
                if new_state == RoomState.ENDED:
                    room.review_started_at = timezone.now()
                elif new_state == RoomState.CLOSED:
                    room.closed_at = timezone.now()

                room.save()

                logger.info(f"房间 {room_code} 状态从 {old_state} 转换到 {new_state}")
                return True, "状态转换成功"

        except Room.DoesNotExist:
            return False, "房间不存在"
        except Exception as e:
            logger.error(f"状态转换失败: {e}", exc_info=True)
            return False, f"状态转换失败: {e}"
    
    def update_host_heartbeat(self, room_code: str):
        """更新房主心跳时间"""
        self._host_heartbeats[room_code] = timezone.now()
    
    def is_host_alive(self, room_code: str) -> bool:
        """检查房主是否在线"""
        last_heartbeat = self._host_heartbeats.get(room_code)
        if not last_heartbeat:
            return False
        
        timeout_threshold = timezone.now() - timedelta(seconds=self.HOST_HEARTBEAT_TIMEOUT)
        return last_heartbeat > timeout_threshold

    @database_sync_to_async
    def _join_room_sync(self, room_code: str, user: User) -> Tuple[bool, str]:
        """同步执行用户加入房间操作"""
        try:
            with transaction.atomic():
                room = Room.objects.select_for_update().get(room_code=room_code)

                # 检查房间是否可以加入
                if not room.can_join():
                    if room.is_expired():
                        return False, "房间已过期"
                    elif room.is_full():
                        return False, f"房间已满，最大人数: {room.max_participants}"
                    else:
                        return False, f"房间状态不允许加入: {room.get_status_display()}"

                # 检查用户是否已在房间中
                if RoomParticipant.objects.filter(room=room, user=user, is_active=True).exists():
                    return False, "您已在此房间中"

                # 确定用户状态
                user_state = UserState.SPECTATING if room.status == RoomState.IN_PROGRESS else UserState.JOINED

                # 添加用户到房间
                RoomParticipant.objects.create(
                    room=room,
                    user=user,
                    role=RoomParticipant.ROLE_PARTICIPANT,
                    state=user_state
                )

                logger.info(f"用户 {user.username} 成功加入房间 {room_code}")
                return True, "成功加入房间"

        except Room.DoesNotExist:
            return False, "房间不存在"
        except Exception as e:
            logger.error(f"用户加入房间失败: {e}", exc_info=True)
            return False, f"加入房间失败: {e}"

    def join_room_sync(self, room_code: str, user: User) -> Tuple[bool, str]:
        """
        同步用户加入房间 - 用于Django视图

        Args:
            room_code: 房间代码
            user: 用户对象

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            with transaction.atomic():
                room = Room.objects.select_related('host', 'event_template').get(
                    room_code=room_code
                )

                # 检查房间状态
                if room.status == RoomState.CLOSED:
                    return False, "房间已关闭，无法加入"

                # 检查用户是否已在房间中
                existing_participant = RoomParticipant.objects.filter(
                    room=room, user=user, left_at__isnull=True
                ).first()

                if existing_participant:
                    return True, "您已在房间中"

                # 检查房间容量
                current_count = RoomParticipant.objects.filter(
                    room=room, left_at__isnull=True
                ).count()

                if current_count >= room.max_participants:
                    return False, "房间已满"

                # 确定用户状态
                user_state = UserState.SPECTATING if room.status == RoomState.IN_PROGRESS else UserState.JOINED

                # 添加用户到房间
                RoomParticipant.objects.create(
                    room=room,
                    user=user,
                    role=RoomParticipant.ROLE_PARTICIPANT,
                    state=user_state
                )

                logger.info(f"用户 {user.username} 成功加入房间 {room_code}")
                return True, "成功加入房间"

        except Room.DoesNotExist:
            return False, "房间不存在"
        except Exception as e:
            logger.error(f"用户加入房间失败: {e}", exc_info=True)
            return False, f"加入房间失败: {e}"

    async def join_room(self, room_code: str, user: User) -> Tuple[bool, str]:
        """
        异步用户加入房间 - 用于WebSocket

        Args:
            room_code: 房间代码
            user: 用户对象

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        async with await self.get_room_lock(room_code):
            return await self._join_room_sync(room_code, user)

    @database_sync_to_async
    def _leave_room_sync(self, room_code: str, user: User) -> Tuple[bool, str]:
        """同步执行用户离开房间操作"""
        try:
            with transaction.atomic():
                room = Room.objects.select_for_update().get(room_code=room_code)

                # 查找用户参与记录
                try:
                    participant = RoomParticipant.objects.get(
                        room=room, user=user, left_at__isnull=True
                    )
                except RoomParticipant.DoesNotExist:
                    return False, "您不在此房间中"

                # 标记用户为离开状态
                participant.left_at = timezone.now()
                participant.save()

                # 如果离开的是房主，需要转移房主权限
                if room.host_id == user.id:
                    success, message = self._transfer_host_sync(room)
                    if not success:
                        # 如果无法转移房主，关闭房间
                        room.status = RoomState.CLOSED
                        room.closed_at = timezone.now()
                        room.save()
                        logger.info(f"房间 {room_code} 因房主离开且无法转移而关闭")

                logger.info(f"用户 {user.username} 离开房间 {room_code}")
                return True, "成功离开房间"

        except Room.DoesNotExist:
            return False, "房间不存在"
        except Exception as e:
            logger.error(f"用户离开房间失败: {e}", exc_info=True)
            return False, f"离开房间失败: {e}"

    def _transfer_host_sync(self, room: Room) -> Tuple[bool, str]:
        """同步执行房主转移（在事务中调用）"""
        # 查找合适的新房主候选人
        candidates = RoomParticipant.objects.filter(
            room=room,
            left_at__isnull=True,
            state__in=[UserState.PLAYING, UserState.READY, UserState.JOINED]
        ).exclude(user=room.host).order_by('joined_at')

        if not candidates.exists():
            return False, "没有合适的房主候选人"

        # 选择最早加入的用户作为新房主
        new_host_participant = candidates.first()
        old_host = room.host

        # 更新房间房主
        room.host = new_host_participant.user
        room.save()

        # 更新参与者角色
        new_host_participant.role = RoomParticipant.ROLE_HOST
        new_host_participant.save()

        logger.info(f"房间 {room.room_code} 房主从 {old_host.username} 转移到 {new_host_participant.user.username}")
        return True, f"房主已转移到 {new_host_participant.user.username}"

    async def leave_room(self, room_code: str, user: User) -> Tuple[bool, str]:
        """
        用户离开房间

        Args:
            room_code: 房间代码
            user: 用户对象

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        async with await self.get_room_lock(room_code):
            return await self._leave_room_sync(room_code, user)

# 全局房间管理器实例
room_manager = RoomManager()
