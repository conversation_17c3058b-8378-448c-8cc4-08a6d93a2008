# core/services/room_lifecycle.py

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from django.utils import timezone
from django.db import transaction
from channels.db import database_sync_to_async

from ..models import Room, RoomParticipant, RoomState, UserState
from .room_manager import room_manager

logger = logging.getLogger(__name__)

class RoomLifecycleManager:
    """
    房间生命周期管理器
    
    职责：
    1. 房间过期检测和自动关闭
    2. 空房间检测和清理
    3. 复盘阶段超时处理
    4. 房主心跳检测和自动转移
    5. 僵尸房间清理
    """
    
    def __init__(self):
        self.cleanup_interval = 60  # 清理检查间隔（秒）
        self.is_running = False
        self._cleanup_task = None
    
    async def start_monitoring(self):
        """启动房间监控任务"""
        if self.is_running:
            logger.warning("房间监控任务已在运行")
            return
        
        self.is_running = True
        self._cleanup_task = asyncio.create_task(self._monitoring_loop())
        logger.info("房间生命周期监控已启动")
    
    async def stop_monitoring(self):
        """停止房间监控任务"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("房间生命周期监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await self._perform_cleanup_cycle()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"房间监控循环出错: {e}", exc_info=True)
                await asyncio.sleep(self.cleanup_interval)
    
    async def _perform_cleanup_cycle(self):
        """执行一次完整的清理周期"""
        logger.debug("开始房间清理周期")
        
        # 1. 检查过期房间
        expired_count = await self._close_expired_rooms()
        
        # 2. 检查复盘超时房间
        review_timeout_count = await self._close_review_timeout_rooms()
        
        # 3. 检查空房间
        empty_count = await self._close_empty_rooms()
        
        # 4. 检查房主心跳
        host_transfer_count = await self._check_host_heartbeats()
        
        # 5. 清理已关闭的房间
        destroyed_count = await self._destroy_closed_rooms()
        
        if any([expired_count, review_timeout_count, empty_count, host_transfer_count, destroyed_count]):
            logger.info(
                f"清理周期完成 - 过期: {expired_count}, 复盘超时: {review_timeout_count}, "
                f"空房间: {empty_count}, 房主转移: {host_transfer_count}, 销毁: {destroyed_count}"
            )
    
    @database_sync_to_async
    def _get_expired_rooms(self) -> List[str]:
        """获取过期房间列表"""
        now = timezone.now()
        expired_rooms = Room.objects.filter(
            expires_at__lt=now,
            status__in=[RoomState.OPEN, RoomState.IN_PROGRESS, RoomState.ENDED]
        ).values_list('room_code', flat=True)
        return list(expired_rooms)
    
    async def _close_expired_rooms(self) -> int:
        """关闭过期房间"""
        expired_room_codes = await self._get_expired_rooms()
        count = 0
        
        for room_code in expired_room_codes:
            try:
                success, message = await room_manager.transition_room_state(
                    room_code, RoomState.CLOSED
                )
                if success:
                    count += 1
                    logger.info(f"房间 {room_code} 因过期被关闭")
            except Exception as e:
                logger.error(f"关闭过期房间 {room_code} 失败: {e}")
        
        return count
    
    @database_sync_to_async
    def _get_review_timeout_rooms(self) -> List[str]:
        """获取复盘超时房间列表"""
        timeout_threshold = timezone.now() - timedelta(minutes=15)  # 15分钟超时
        timeout_rooms = Room.objects.filter(
            status=RoomState.ENDED,
            review_started_at__lt=timeout_threshold
        ).values_list('room_code', flat=True)
        return list(timeout_rooms)
    
    async def _close_review_timeout_rooms(self) -> int:
        """关闭复盘超时房间"""
        timeout_room_codes = await self._get_review_timeout_rooms()
        count = 0
        
        for room_code in timeout_room_codes:
            try:
                success, message = await room_manager.transition_room_state(
                    room_code, RoomState.CLOSED
                )
                if success:
                    count += 1
                    logger.info(f"房间 {room_code} 因复盘超时被关闭")
            except Exception as e:
                logger.error(f"关闭复盘超时房间 {room_code} 失败: {e}")
        
        return count
    
    @database_sync_to_async
    def _get_empty_rooms(self) -> List[str]:
        """获取空房间列表"""
        timeout_threshold = timezone.now() - timedelta(minutes=5)  # 5分钟无活跃用户
        
        # 查找没有活跃参与者的房间
        empty_rooms = []
        for room in Room.objects.filter(
            status__in=[RoomState.OPEN, RoomState.IN_PROGRESS],
            last_activity_at__lt=timeout_threshold
        ):
            active_participants = RoomParticipant.objects.filter(
                room=room, is_active=True
            ).count()
            if active_participants == 0:
                empty_rooms.append(room.room_code)
        
        return empty_rooms
    
    async def _close_empty_rooms(self) -> int:
        """关闭空房间"""
        empty_room_codes = await self._get_empty_rooms()
        count = 0
        
        for room_code in empty_room_codes:
            try:
                success, message = await room_manager.transition_room_state(
                    room_code, RoomState.CLOSED
                )
                if success:
                    count += 1
                    logger.info(f"房间 {room_code} 因长时间无活跃用户被关闭")
            except Exception as e:
                logger.error(f"关闭空房间 {room_code} 失败: {e}")
        
        return count
    
    async def _check_host_heartbeats(self) -> int:
        """检查房主心跳并执行转移"""
        # 这个功能将在后续实现，需要与WebSocket消费者集成
        return 0
    
    @database_sync_to_async
    def _destroy_closed_rooms(self) -> int:
        """销毁长时间关闭的房间"""
        destroy_threshold = timezone.now() - timedelta(hours=24)  # 24小时后销毁
        
        rooms_to_destroy = Room.objects.filter(
            status=RoomState.CLOSED,
            closed_at__lt=destroy_threshold
        )
        
        count = rooms_to_destroy.count()
        if count > 0:
            # 记录要销毁的房间
            room_codes = list(rooms_to_destroy.values_list('room_code', flat=True))
            
            # 执行删除（会级联删除相关记录）
            with transaction.atomic():
                rooms_to_destroy.delete()
            
            logger.info(f"销毁了 {count} 个长时间关闭的房间: {room_codes}")
        
        return count
    
    async def force_cleanup_room(self, room_code: str) -> bool:
        """强制清理指定房间"""
        try:
            success, message = await room_manager.transition_room_state(
                room_code, RoomState.CLOSED
            )
            if success:
                logger.info(f"强制关闭房间 {room_code}: {message}")
            return success
        except Exception as e:
            logger.error(f"强制清理房间 {room_code} 失败: {e}")
            return False

# 全局生命周期管理器实例
lifecycle_manager = RoomLifecycleManager()
