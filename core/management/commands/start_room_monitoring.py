# core/management/commands/start_room_monitoring.py

import asyncio
import logging
from django.core.management.base import BaseCommand
from core.services.room_lifecycle import lifecycle_manager

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    """
    启动房间生命周期监控的Django管理命令
    
    用法:
    python manage.py start_room_monitoring
    """
    
    help = '启动房间生命周期监控服务'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='监控检查间隔（秒），默认60秒'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='启用详细日志输出'
        )
    
    def handle(self, *args, **options):
        """执行命令"""
        interval = options['interval']
        verbose = options['verbose']
        
        if verbose:
            logging.basicConfig(level=logging.DEBUG)
        
        # 设置监控间隔
        lifecycle_manager.cleanup_interval = interval
        
        self.stdout.write(
            self.style.SUCCESS(f'启动房间生命周期监控，检查间隔: {interval}秒')
        )
        
        try:
            # 运行异步监控循环
            asyncio.run(self._run_monitoring())
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('收到中断信号，正在停止监控...')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'监控服务出错: {e}')
            )
            raise
        finally:
            self.stdout.write(
                self.style.SUCCESS('房间生命周期监控已停止')
            )
    
    async def _run_monitoring(self):
        """运行监控循环"""
        await lifecycle_manager.start_monitoring()
        
        try:
            # 保持运行直到收到中断信号
            while True:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
        finally:
            await lifecycle_manager.stop_monitoring()
