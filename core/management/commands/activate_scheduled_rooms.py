# core/management/commands/activate_scheduled_rooms.py

import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from core.models import Room, RoomState
from core.services.room_manager import room_manager

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    """
    激活预约房间的Django管理命令

    此命令会查找所有已到开始时间的预约房间，并将其状态从SCHEDULED转换为OPEN。
    这是预约系统与实时房间系统解耦的关键组件。
    
    用法:
    python manage.py activate_scheduled_rooms
    python manage.py activate_scheduled_rooms --dry-run  # 仅显示将要激活的房间，不实际执行
    python manage.py activate_scheduled_rooms --verbose  # 显示详细日志
    """
    
    help = '激活已到开始时间的预约房间'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要激活的房间，不实际执行激活操作'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='启用详细日志输出'
        )
        
        parser.add_argument(
            '--check-interval',
            type=int,
            default=60,
            help='检查间隔（秒），仅在持续运行模式下有效，默认60秒'
        )
        
        parser.add_argument(
            '--continuous',
            action='store_true',
            help='持续运行模式，每隔指定间隔检查一次'
        )
    
    def handle(self, *args, **options):
        """执行命令"""
        dry_run = options['dry_run']
        verbose = options['verbose']
        continuous = options['continuous']
        check_interval = options['check_interval']
        
        if verbose:
            logging.basicConfig(level=logging.DEBUG)
        
        if continuous:
            self.stdout.write(
                self.style.SUCCESS(f'启动持续激活模式，检查间隔: {check_interval}秒')
            )
            self._run_continuous(dry_run, check_interval)
        else:
            self.stdout.write(
                self.style.SUCCESS('执行单次预约房间激活检查')
            )
            activated_count = self._activate_ready_rooms(dry_run)
            
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'[DRY RUN] 找到 {activated_count} 个可激活的预约房间')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'成功激活 {activated_count} 个预约房间')
                )
    
    def _run_continuous(self, dry_run, check_interval):
        """持续运行模式"""
        import time
        
        try:
            while True:
                activated_count = self._activate_ready_rooms(dry_run)
                
                if activated_count > 0:
                    if dry_run:
                        self.stdout.write(
                            self.style.WARNING(f'[DRY RUN] 找到 {activated_count} 个可激活的预约房间')
                        )
                    else:
                        self.stdout.write(
                            self.style.SUCCESS(f'激活了 {activated_count} 个预约房间')
                        )
                
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('收到中断信号，正在停止持续激活服务...')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'持续激活服务出错: {e}')
            )
            raise
        finally:
            self.stdout.write(
                self.style.SUCCESS('预约房间激活服务已停止')
            )
    
    def _activate_ready_rooms(self, dry_run=False):
        """
        激活已到开始时间的预约房间
        
        Args:
            dry_run (bool): 如果为True，仅返回可激活房间数量，不实际执行激活
            
        Returns:
            int: 激活的房间数量
        """
        now = timezone.now()
        
        # 查找所有已到开始时间的预约房间
        ready_rooms = Room.objects.filter(
            status=RoomState.SCHEDULED,
            scheduled_start_time__lte=now
        ).select_related('host', 'event_template')
        
        activated_count = 0
        
        for room in ready_rooms:
            try:
                if dry_run:
                    # 仅记录将要激活的房间
                    logger.info(f"[DRY RUN] 将激活预约房间: {room.room_code}, "
                              f"房主: {room.host.username}, "
                              f"预约时间: {room.scheduled_start_time}")
                    activated_count += 1
                else:
                    # 实际激活房间
                    success = self._activate_room(room)
                    if success:
                        activated_count += 1
                        logger.info(f"成功激活预约房间: {room.room_code}, "
                                  f"房主: {room.host.username}, "
                                  f"预约时间: {room.scheduled_start_time}")
                    else:
                        logger.error(f"激活预约房间失败: {room.room_code}")
                        
            except Exception as e:
                logger.error(f"处理预约房间 {room.room_code} 时发生错误: {e}", exc_info=True)
        
        return activated_count
    
    def _activate_room(self, room):
        """
        激活单个预约房间
        
        Args:
            room (Room): 要激活的房间对象
            
        Returns:
            bool: 激活是否成功
        """
        try:
            with transaction.atomic():
                # 使用房间管理器进行状态转换，确保遵循状态机规则
                success, message = room_manager.transition_room_state_sync(
                    room.room_code,
                    RoomState.OPEN  # 预约房间激活后变为OPEN状态
                )
                
                if success:
                    # 更新房间的开启时间和最后活动时间
                    room.opened_at = timezone.now()
                    room.last_activity_at = timezone.now()
                    room.save(update_fields=['opened_at', 'last_activity_at'])

                    logger.info(f"预约房间 {room.room_code} 已激活为开启状态")
                    return True
                else:
                    logger.error(f"激活预约房间 {room.room_code} 失败: {message}")
                    return False
                    
        except Exception as e:
            logger.error(f"激活预约房间 {room.room_code} 时发生异常: {e}", exc_info=True)
            return False
