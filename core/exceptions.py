# core/exceptions.py

"""
团建游戏房间系统专用异常定义

这些异常类提供了清晰的错误分类和处理机制，
支持国际化和详细的错误信息传递。
"""

class RoomSystemException(Exception):
    """房间系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> dict:
        """转换为字典格式，便于API响应"""
        return {
            'error': self.error_code,
            'message': self.message,
            'details': self.details
        }

class RoomNotFoundException(RoomSystemException):
    """房间不存在异常"""
    pass

class PermissionDeniedException(RoomSystemException):
    """权限不足异常"""
    pass

class RoomFullException(RoomSystemException):
    """房间已满异常"""
    
    def __init__(self, current_count: int, max_count: int):
        message = f"房间已满，当前人数: {current_count}/{max_count}"
        details = {
            'current_participants': current_count,
            'max_participants': max_count
        }
        super().__init__(message, details=details)

class RoomExpiredException(RoomSystemException):
    """房间已过期异常"""
    
    def __init__(self, expired_at: str):
        message = f"房间已过期，过期时间: {expired_at}"
        details = {'expired_at': expired_at}
        super().__init__(message, details=details)

class InvalidStateTransitionException(RoomSystemException):
    """无效状态转换异常"""
    
    def __init__(self, current_state: str, target_state: str):
        message = f"无法从状态 {current_state} 转换到 {target_state}"
        details = {
            'current_state': current_state,
            'target_state': target_state
        }
        super().__init__(message, details=details)

class UserAlreadyInRoomException(RoomSystemException):
    """用户已在房间中异常"""
    pass

class UserNotInRoomException(RoomSystemException):
    """用户不在房间中异常"""
    pass

class HostTransferException(RoomSystemException):
    """房主转移异常"""
    pass

class GameStateException(RoomSystemException):
    """游戏状态异常"""
    pass

class ValidationException(RoomSystemException):
    """数据验证异常"""
    pass

class ConcurrencyException(RoomSystemException):
    """并发操作异常"""
    pass

class ResourceLimitException(RoomSystemException):
    """资源限制异常"""
    
    def __init__(self, resource_type: str, current: int, limit: int):
        message = f"{resource_type}超出限制，当前: {current}，限制: {limit}"
        details = {
            'resource_type': resource_type,
            'current': current,
            'limit': limit
        }
        super().__init__(message, details=details)

class SubscriptionLimitException(RoomSystemException):
    """订阅限制异常"""
    
    def __init__(self, feature: str, required_level: str, current_level: str):
        message = f"功能 {feature} 需要 {required_level} 订阅，当前: {current_level}"
        details = {
            'feature': feature,
            'required_level': required_level,
            'current_level': current_level
        }
        super().__init__(message, details=details)

# 异常处理工具函数
def handle_room_exception(exception: Exception) -> dict:
    """
    统一处理房间系统异常，返回标准化的错误响应
    
    Args:
        exception: 异常对象
        
    Returns:
        dict: 标准化的错误响应
    """
    if isinstance(exception, RoomSystemException):
        return exception.to_dict()
    
    # 处理其他类型的异常
    return {
        'error': 'UnknownError',
        'message': str(exception),
        'details': {}
    }
