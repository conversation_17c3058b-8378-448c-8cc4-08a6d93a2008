# Generated by Django 5.2.4 on 2025-07-14 16:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0013_remove_roomtemplate_creator_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('template_config', models.JSO<PERSON>ield(default=dict, help_text='模板的步骤配置')),
                ('required_subscription', models.CharField(choices=[('FREE', '免费版'), ('PRO', 'Pro版'), ('MAX', 'Max版')], default='FREE', help_text='使用此模板所需的最低订阅等级', max_length=10)),
            ],
            options={
                'verbose_name': '系统模板',
                'verbose_name_plural': '系统模板',
                'ordering': ['id'],
            },
        ),
    ]
