# Generated by Django 5.2.3 on 2025-07-07 19:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_room_current_step_order_room_event_template_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='subscription_level',
            field=models.CharField(choices=[('Free', 'Free'), ('Pro', 'Pro'), ('Max', 'Max')], default='Free', help_text='User subscription level determining available features', max_length=10),
        ),
    ]
