# Generated by Django 5.2.4 on 2025-07-11 17:47

from django.db import migrations


def migrate_existing_data(apps, schema_editor):
    """
    迁移现有数据到新的数据库结构

    1. 为现有用户分配默认用户组
    2. 为房主创建RoomParticipant记录
    3. 迁移现有的得分数据（如果存在PlayerScore模型）
    """

    # 获取模型
    User = apps.get_model('core', 'User')
    Room = apps.get_model('core', 'Room')
    RoomParticipant = apps.get_model('core', 'RoomParticipant')

    # 尝试获取PlayerScore模型（可能不存在）
    try:
        PlayerScore = apps.get_model('games', 'PlayerScore')
        has_player_score = True
    except LookupError:
        has_player_score = False
        PlayerScore = None

    # 获取Group模型
    Group = apps.get_model('auth', 'Group')

    print("开始迁移现有数据...")

    # 1. 为现有用户分配默认用户组
    try:
        regular_users_group = Group.objects.get(name='Regular Users')

        users_without_groups = User.objects.filter(groups__isnull=True)
        for user in users_without_groups:
            # 根据现有的is_superuser和is_staff字段分配组
            if user.is_superuser:
                try:
                    super_admin_group = Group.objects.get(name='Super Administrators')
                    user.groups.add(super_admin_group)
                except Group.DoesNotExist:
                    user.groups.add(regular_users_group)
            elif user.is_staff:
                try:
                    staff_group = Group.objects.get(name='Staff')
                    user.groups.add(staff_group)
                except Group.DoesNotExist:
                    user.groups.add(regular_users_group)
            else:
                user.groups.add(regular_users_group)

        print(f"为 {users_without_groups.count()} 个用户分配了默认用户组")
    except Group.DoesNotExist:
        print("警告：未找到用户组，跳过用户组分配")

    # 2. 为房主创建RoomParticipant记录
    rooms_migrated = 0
    for room in Room.objects.all():
        # 为房主创建RoomParticipant记录
        host_participant, created = RoomParticipant.objects.get_or_create(
            room=room,
            user=room.host,
            defaults={
                'role': 'host',
                'is_active': True,
                'score': 0
            }
        )
        if created:
            rooms_migrated += 1

    print(f"为 {rooms_migrated} 个房间创建了房主参与者记录")

    # 3. 迁移得分数据（如果存在PlayerScore模型）
    if has_player_score and PlayerScore:
        scores_migrated = 0
        for score in PlayerScore.objects.all():
            # 查找或创建对应的RoomParticipant记录
            participant, created = RoomParticipant.objects.get_or_create(
                room=score.room,
                user=score.player,
                defaults={
                    'role': 'participant',
                    'is_active': True,
                    'score': score.score
                }
            )

            # 如果记录已存在，更新得分
            if not created:
                participant.score = score.score
                participant.save()

            scores_migrated += 1

        print(f"迁移了 {scores_migrated} 条得分记录")
    else:
        print("未找到PlayerScore模型，跳过得分数据迁移")

    print("数据迁移完成！")


def reverse_migration(apps, schema_editor):
    """
    反向迁移：清理创建的数据
    """
    RoomParticipant = apps.get_model('core', 'RoomParticipant')
    RoomParticipant.objects.all().delete()
    print("已清理RoomParticipant数据")


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_setup_user_groups'),
        ('games', '0002_playerscore'),  # 确保games应用的迁移已完成
    ]

    operations = [
        migrations.RunPython(migrate_existing_data, reverse_migration),
    ]
