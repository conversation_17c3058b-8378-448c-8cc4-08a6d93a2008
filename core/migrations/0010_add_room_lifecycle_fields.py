# Generated by Django 5.2.4 on 2025-07-12 17:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_room_closed_at_room_expires_at_alter_room_status_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='roomparticipant',
            name='is_ready',
        ),
        migrations.AddField(
            model_name='room',
            name='last_activity_at',
            field=models.DateTimeField(auto_now=True, help_text='最后活动时间，用于检测空房间'),
        ),
        migrations.AddField(
            model_name='room',
            name='review_started_at',
            field=models.DateTimeField(blank=True, help_text='复盘阶段开始时间', null=True),
        ),
        migrations.AddField(
            model_name='roomparticipant',
            name='left_at',
            field=models.DateTimeField(blank=True, help_text='用户离开房间的时间', null=True),
        ),
        migrations.AddField(
            model_name='roomparticipant',
            name='state',
            field=models.CharField(choices=[('JOINED', '已加入'), ('READY', '准备就绪'), ('PLAYING', '游戏中'), ('SPECTATING', '观战中')], default='JOINED', help_text='用户在房间中的状态', max_length=20),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('WAITING', '等待中'), ('ACTIVE', '活跃中'), ('REVIEW', '结算/复盘中'), ('CLOSED', '已关闭')], default='WAITING', max_length=20),
        ),
    ]
