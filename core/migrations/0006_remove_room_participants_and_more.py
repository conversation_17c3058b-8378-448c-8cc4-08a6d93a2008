# Generated by Django 5.2.4 on 2025-07-11 17:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_room_duration_hours_room_max_participants'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='room',
            name='participants',
        ),
        migrations.AlterField(
            model_name='user',
            name='subscription_level',
            field=models.CharField(choices=[('Free', 'Free'), ('Pro', 'Pro'), ('Max', 'Max')], default='Free', help_text='用户订阅等级，决定可用功能范围', max_length=10),
        ),
        migrations.CreateModel(
            name='RoomParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True, help_text='用户加入房间的时间')),
                ('last_active_at', models.DateTimeField(auto_now=True, help_text='用户最后活跃时间')),
                ('role', models.CharField(choices=[('participant', '参与者'), ('moderator', '协管员'), ('host', '房主')], default='participant', help_text='用户在房间中的角色', max_length=20)),
                ('score', models.IntegerField(default=0, help_text='用户在房间中的总得分')),
                ('is_ready', models.BooleanField(default=False, help_text='用户是否已准备开始游戏')),
                ('is_active', models.BooleanField(default=True, help_text='用户是否仍在房间中（未退出）')),
                ('custom_data', models.JSONField(blank=True, default=dict, help_text='自定义数据字段，用于存储特殊游戏状态等')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_participants', to='core.room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '房间参与者',
                'verbose_name_plural': '房间参与者',
                'ordering': ['-joined_at'],
                'unique_together': {('room', 'user')},
            },
        ),
    ]
