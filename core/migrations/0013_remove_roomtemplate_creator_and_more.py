# Generated by Django 5.2.4 on 2025-07-14 14:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0012_add_room_template_system'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='templatestep',
            unique_together=None,
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('SCHEDULED', '已预约'), ('OPEN', '已开启'), ('WAITING_FOR_HOST', '等待房主'), ('READY', '准备就绪'), ('IN_PROGRESS', '活动中'), ('ENDED', '已结束'), ('CLOSED', '已关闭')], default='OPEN', max_length=20),
        ),
        migrations.RemoveField(
            model_name='roomtemplate',
            name='creator',
        ),
        migrations.RemoveField(
            model_name='templatestep',
            name='template',
        ),
        migrations.DeleteModel(
            name='RoomStep',
        ),
        migrations.DeleteModel(
            name='RoomTemplate',
        ),
        migrations.DeleteModel(
            name='TemplateStep',
        ),
    ]
