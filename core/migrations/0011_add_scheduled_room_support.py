# Generated by Django 5.2.4 on 2025-07-14 06:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_add_room_lifecycle_fields'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='room',
            name='room_status_created_idx',
        ),
        migrations.RemoveIndex(
            model_name='room',
            name='room_expires_idx',
        ),
        migrations.RemoveIndex(
            model_name='room',
            name='room_closed_idx',
        ),
        migrations.RemoveIndex(
            model_name='room',
            name='room_host_idx',
        ),
        migrations.RemoveIndex(
            model_name='room',
            name='room_code_idx',
        ),
        migrations.RemoveIndex(
            model_name='roomparticipant',
            name='room_active_participants_idx',
        ),
        migrations.RemoveIndex(
            model_name='roomparticipant',
            name='user_active_rooms_idx',
        ),
        migrations.RemoveIndex(
            model_name='roomparticipant',
            name='role_active_idx',
        ),
        migrations.RemoveIndex(
            model_name='roomparticipant',
            name='last_active_idx',
        ),
        migrations.AddField(
            model_name='room',
            name='scheduled_start_time',
            field=models.DateTimeField(blank=True, help_text='预约的开始时间，仅对SCHEDULED状态的房间有效', null=True),
        ),
        migrations.AlterField(
            model_name='room',
            name='last_activity_at',
            field=models.DateTimeField(auto_now_add=True, help_text='最后活动时间，用于检测空房间'),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('SCHEDULED', '已预约'), ('WAITING', '等待中'), ('ACTIVE', '活跃中'), ('REVIEW', '结算/复盘中'), ('CLOSED', '已关闭')], default='WAITING', max_length=20),
        ),
    ]
