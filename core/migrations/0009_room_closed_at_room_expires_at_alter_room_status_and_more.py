# Generated by Django 5.2.4 on 2025-07-12 10:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0008_migrate_existing_data'),
        ('events', '0004_alter_eventstep_step_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='room',
            name='closed_at',
            field=models.DateTimeField(blank=True, help_text='房间关闭时间，用于计算销毁时间', null=True),
        ),
        migrations.AddField(
            model_name='room',
            name='expires_at',
            field=models.DateTimeField(blank=True, help_text='房间过期时间，超过此时间房间将被关闭', null=True),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('WAITING', 'Waiting for players'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished'), ('CLOSED', 'Closed')], default='WAITING', max_length=20),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['status', 'created_at'], name='room_status_created_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['expires_at'], name='room_expires_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['closed_at'], name='room_closed_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['host'], name='room_host_idx'),
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['room_code'], name='room_code_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['room', 'is_active'], name='room_active_participants_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['user', 'is_active'], name='user_active_rooms_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['role', 'is_active'], name='role_active_idx'),
        ),
        migrations.AddIndex(
            model_name='roomparticipant',
            index=models.Index(fields=['last_active_at'], name='last_active_idx'),
        ),
    ]
