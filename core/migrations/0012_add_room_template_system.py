# Generated by Django 5.2.4 on 2025-07-14 10:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_add_scheduled_room_support'),
    ]

    operations = [
        migrations.CreateModel(
            name='RoomTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='模板名称', max_length=100)),
                ('description', models.TextField(blank=True, help_text='模板描述')),
                ('template_type', models.CharField(choices=[('DEFAULT', '系统默认'), ('USER', '用户自定义')], default='DEFAULT', help_text='模板类型', max_length=20)),
                ('min_subscription_level', models.CharField(choices=[('Free', 'Free'), ('Pro', 'Pro'), ('Max', 'Max')], default='Free', help_text='使用此模板所需的最低订阅等级', max_length=10)),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='使用次数统计')),
                ('is_active', models.BooleanField(default=True, help_text='模板是否可用')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='排序权重，数值越小越靠前')),
                ('creator', models.ForeignKey(blank=True, help_text='模板创建者，系统默认模板此字段为空', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='room_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '房间模板',
                'verbose_name_plural': '房间模板',
                'ordering': ['sort_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='TemplateStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, help_text='步骤自定义名称，为空时使用默认名称', max_length=100)),
                ('step_type', models.CharField(choices=[('GAME_PICTIONARY', '游戏：你画我猜'), ('FREE_CHAT', '自由讨论'), ('PAUSE', '暂停环节'), ('SPEECH', '发言环节'), ('CUSTOM', '自定义环节'), ('POLL', '投票环节'), ('QNA', '问答环节')], help_text='步骤类型', max_length=50)),
                ('order', models.PositiveIntegerField(help_text='步骤在模板中的顺序')),
                ('configuration', models.JSONField(blank=True, default=dict, help_text='步骤配置参数，JSON格式')),
                ('duration', models.PositiveIntegerField(default=300, help_text='步骤持续时间（秒）')),
                ('is_required', models.BooleanField(default=True, help_text='是否为必需步骤')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='core.roomtemplate')),
            ],
            options={
                'verbose_name': '模板步骤',
                'verbose_name_plural': '模板步骤',
                'ordering': ['template', 'order'],
            },
        ),
        migrations.CreateModel(
            name='RoomStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='步骤名称', max_length=100)),
                ('step_type', models.CharField(help_text='步骤类型', max_length=50)),
                ('order', models.PositiveIntegerField(help_text='步骤顺序')),
                ('configuration', models.JSONField(blank=True, default=dict)),
                ('duration', models.PositiveIntegerField(default=300)),
                ('status', models.CharField(choices=[('PENDING', '等待执行'), ('ACTIVE', '正在执行'), ('COMPLETED', '已完成'), ('SKIPPED', '已跳过')], default='PENDING', max_length=20)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('result_data', models.JSONField(blank=True, default=dict, help_text='步骤执行结果数据')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_steps', to='core.room')),
            ],
            options={
                'verbose_name': '房间步骤',
                'verbose_name_plural': '房间步骤',
                'ordering': ['room', 'order'],
                'unique_together': {('room', 'order')},
            },
        ),
        migrations.AddIndex(
            model_name='roomtemplate',
            index=models.Index(fields=['template_type', 'is_active'], name='core_roomte_templat_d24d32_idx'),
        ),
        migrations.AddIndex(
            model_name='roomtemplate',
            index=models.Index(fields=['sort_order'], name='core_roomte_sort_or_599c50_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='templatestep',
            unique_together={('template', 'order')},
        ),
    ]
