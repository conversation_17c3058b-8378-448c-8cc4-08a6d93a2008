# core/middleware/session_management.py

import logging
import time
from datetime import datetime, timedelta
from django.utils import timezone
from django.http import JsonResponse
from django.conf import settings
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)
User = get_user_model()

class SessionTimeoutMiddleware:
    """
    会话超时中间件
    
    功能：
    1. 检查JWT token是否过期
    2. 在30分钟无活动后自动清理会话
    3. 提供友好的过期响应
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.session_timeout = getattr(settings, 'SESSION_TIMEOUT_MINUTES', 30) * 60  # 转换为秒
        
    def __call__(self, request):
        # 检查是否是API请求
        if request.path.startswith('/api/'):
            # 检查认证状态
            auth_result = self.check_authentication(request)
            if auth_result:
                return auth_result
                
            # 更新最后活动时间
            self.update_last_activity(request)
        
        response = self.get_response(request)
        return response
    
    def check_authentication(self, request):
        """检查认证状态"""
        # 跳过不需要认证的端点
        skip_auth_paths = [
            '/api/token/',
            '/api/register/',
            '/api/health-check/',
            '/api/',
        ]
        
        if any(request.path.startswith(path) for path in skip_auth_paths):
            return None
        
        # 检查Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning(f"API请求缺少认证头: {request.path}")
            return JsonResponse({
                'error': 'Authentication required',
                'code': 'AUTH_REQUIRED',
                'redirect_to_login': True
            }, status=401)
        
        # 提取token
        token = auth_header.split(' ')[1]
        
        try:
            # 验证token
            UntypedToken(token)
            
            # 检查会话超时
            if self.is_session_expired(request):
                logger.info(f"用户会话超时: {request.user.username if hasattr(request, 'user') else 'Unknown'}")
                return JsonResponse({
                    'error': 'Session expired due to inactivity',
                    'code': 'SESSION_TIMEOUT',
                    'redirect_to_login': True,
                    'timeout_minutes': self.session_timeout // 60
                }, status=401)
                
        except (InvalidToken, TokenError) as e:
            logger.warning(f"无效的JWT token: {e}")
            return JsonResponse({
                'error': 'Invalid or expired token',
                'code': 'TOKEN_INVALID',
                'redirect_to_login': True
            }, status=401)
        
        return None
    
    def update_last_activity(self, request):
        """更新最后活动时间"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 在session中存储最后活动时间
            request.session['last_activity'] = time.time()
            request.session.modified = True
    
    def is_session_expired(self, request):
        """检查会话是否过期"""
        if not hasattr(request, 'session'):
            return False
            
        last_activity = request.session.get('last_activity')
        if not last_activity:
            # 首次访问，设置当前时间
            request.session['last_activity'] = time.time()
            return False
        
        # 检查是否超过超时时间
        current_time = time.time()
        return (current_time - last_activity) > self.session_timeout

class WebSocketSessionMiddleware(BaseMiddleware):
    """
    WebSocket会话管理中间件
    
    功能：
    1. 处理WebSocket连接的认证
    2. 管理WebSocket会话超时
    3. 自动断开过期连接
    """
    
    def __init__(self, inner):
        super().__init__(inner)
        self.session_timeout = getattr(settings, 'WEBSOCKET_TIMEOUT_MINUTES', 30) * 60
        
    async def __call__(self, scope, receive, send):
        # 只处理WebSocket连接
        if scope['type'] != 'websocket':
            return await super().__call__(scope, receive, send)
        
        # 检查WebSocket认证
        user = await self.get_user_from_token(scope)
        scope['user'] = user
        
        if isinstance(user, AnonymousUser):
            # 拒绝未认证的连接
            await send({
                'type': 'websocket.close',
                'code': 4001,  # 自定义关闭代码：认证失败
            })
            return
        
        # 设置连接时间
        scope['connected_at'] = time.time()
        
        return await super().__call__(scope, receive, send)
    
    @database_sync_to_async
    def get_user_from_token(self, scope):
        """从token获取用户"""
        try:
            # 从查询参数获取token
            query_string = scope.get('query_string', b'').decode('utf-8')
            if 'token=' not in query_string:
                logger.warning("WebSocket连接缺少token参数")
                return AnonymousUser()
            
            token = query_string.split('token=')[1].split('&')[0]
            
            # 验证token
            validated_token = UntypedToken(token)
            user_id = validated_token.get('user_id')
            
            user = User.objects.get(id=user_id)
            logger.info(f"WebSocket用户认证成功: {user.username}")
            return user
            
        except (InvalidToken, TokenError, User.DoesNotExist) as e:
            logger.warning(f"WebSocket认证失败: {e}")
            return AnonymousUser()
        except Exception as e:
            logger.error(f"WebSocket认证过程中出现错误: {e}")
            return AnonymousUser()

class ConnectionTimeoutMiddleware:
    """
    连接超时中间件
    
    处理长时间运行的请求，防止资源泄露
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.request_timeout = getattr(settings, 'REQUEST_TIMEOUT_SECONDS', 30)
    
    def __call__(self, request):
        # 记录请求开始时间
        start_time = time.time()
        
        try:
            response = self.get_response(request)
            
            # 记录请求处理时间
            processing_time = time.time() - start_time
            if processing_time > 5:  # 超过5秒的请求记录警告
                logger.warning(f"慢请求: {request.path} 耗时 {processing_time:.2f}秒")
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"请求处理异常: {request.path} 耗时 {processing_time:.2f}秒, 错误: {e}")
            
            # 返回友好的错误响应
            if request.path.startswith('/api/'):
                return JsonResponse({
                    'error': 'Request processing failed',
                    'code': 'PROCESSING_ERROR',
                    'details': str(e) if settings.DEBUG else 'Internal server error'
                }, status=500)
            
            raise
