# core/middleware.py

import logging # 引入 logging 模块
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from channels.middleware import BaseMiddleware
from urllib.parse import parse_qs
from rest_framework_simplejwt.tokens import AccessToken, UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.conf import settings

# 获取 logger 实例
logger = logging.getLogger(__name__)
User = get_user_model()

@database_sync_to_async
def get_user(user_id):
    try:
        return User.objects.get(id=user_id)
    except User.DoesNotExist:
        return AnonymousUser()

class TokenAuthMiddleware(BaseMiddleware):
    """
    调试诊断版的WebSocket Token认证中间件。
    增加了详细的日志输出，以定位身份识别失败的根本原因。
    """
    async def __call__(self, scope, receive, send):
        query_string = scope.get("query_string", b"").decode("utf-8")
        query_params = parse_qs(query_string)
        token = query_params.get("token", [None])[0]

        if token:
            logger.info("--- [TokenAuthMiddleware] 开始验证Token ---")
            try:
                # 1. 验证Token类型和签名
                access_token = AccessToken(token)
                
                # --- 关键诊断点 1 ---
                # 打印出解码后的完整Token内容，让我们看看里面到底有什么
                logger.info(f"[TokenAuthMiddleware] 解码后的Token Payload: {access_token.payload}")
                
                # 2. 获取settings中定义的用户ID字段
                user_id_field = settings.SIMPLE_JWT.get('USER_ID_FIELD', 'user_id')
                logger.info(f"[TokenAuthMiddleware] 期望的用户ID字段名: '{user_id_field}'")
                
                # 3. 从Token中提取用户ID
                user_id = access_token[user_id_field]
                logger.info(f"[TokenAuthMiddleware] 成功提取用户ID: {user_id}")
                
                # 4. 从数据库中获取用户对象
                scope['user'] = await get_user(user_id=user_id)
                logger.info(f"[TokenAuthMiddleware] 成功获取用户: {scope['user']}")

            except (InvalidToken, TokenError, KeyError) as e:
                # --- 关键诊断点 2 ---
                # 如果发生任何错误，打印出详细的错误信息
                logger.error(f"[TokenAuthMiddleware] Token验证失败! 错误类型: {type(e).__name__}, 错误信息: {e}")
                scope['user'] = AnonymousUser()
            
            logger.info("--- [TokenAuthMiddleware] 验证结束 ---")
        else:
            logger.warning("[TokenAuthMiddleware] URL中未找到Token，设置为匿名用户。")
            scope['user'] = AnonymousUser()

        return await super().__call__(scope, receive, send)

