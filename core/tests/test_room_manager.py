# core/tests/test_room_manager.py

import asyncio
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from asgiref.sync import sync_to_async

from ..models import Room, RoomParticipant, RoomState, UserState
from ..services.room_manager import room_manager
from ..exceptions import (
    RoomNotFoundException,
    RoomFullException,
    RoomExpiredException,
    InvalidStateTransitionException
)
from events.models import EventTemplate, EventStep

User = get_user_model()

class RoomManagerTestCase(TransactionTestCase):
    """房间管理器测试用例"""

    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.host_user = User.objects.create_user(
            username='testhost',
            email='<EMAIL>',
            password='testpass123'
        )

        self.participant_user = User.objects.create_user(
            username='testparticipant',
            email='<EMAIL>',
            password='testpass123'
        )

        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.host_user
        )

        # 添加测试步骤
        EventStep.objects.create(
            template=self.template,
            step_type=EventStep.STEP_FREE_CHAT,
            order=1,
            duration=300
        )

    def run_async_test(self, coro):
        """运行异步测试的辅助方法"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    def test_create_room_success(self):
        """测试成功创建房间"""
        async def test():
            room = await room_manager.create_room(
                host=self.host_user,
                template_id=self.template.id
            )

            self.assertIsNotNone(room)
            self.assertEqual(room.host, self.host_user)
            self.assertEqual(room.status, RoomState.OPEN)
            self.assertEqual(len(room.room_code), 6)
            return room

        room = self.run_async_test(test())
        self.assertIsNotNone(room)

        # 在同步上下文中检查房主参与者
        host_participant = RoomParticipant.objects.get(
            room=room, user=self.host_user
        )
        self.assertEqual(host_participant.role, RoomParticipant.ROLE_HOST)
        self.assertEqual(host_participant.state, UserState.JOINED)
    
    def test_join_room_success(self):
        """测试成功加入房间"""
        async def test():
            # 创建房间
            room = await room_manager.create_room(
                host=self.host_user,
                template_id=self.template.id
            )

            # 用户加入房间
            success, message = await room_manager.join_room(
                room.room_code, self.participant_user
            )

            self.assertTrue(success)
            self.assertIn("成功", message)

            # 检查参与者记录
            participant = RoomParticipant.objects.get(
                room=room, user=self.participant_user
            )
            self.assertEqual(participant.role, RoomParticipant.ROLE_PARTICIPANT)
            self.assertEqual(participant.state, UserState.JOINED)
            return success

        success = self.run_async_test(test())
        self.assertTrue(success)
    
    def test_join_nonexistent_room(self):
        """测试加入不存在的房间"""
        async def test():
            success, message = await room_manager.join_room(
                "NOTEXIST", self.participant_user
            )

            self.assertFalse(success)
            self.assertIn("不存在", message)
            return success

        success = self.run_async_test(test())
        self.assertFalse(success)
    
    def test_room_state_transition(self):
        """测试房间状态转换"""
        async def test():
            # 创建房间
            room = await room_manager.create_room(
                host=self.host_user,
                template_id=self.template.id
            )

            # 测试有效状态转换
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.IN_PROGRESS, self.host_user
            )
            self.assertTrue(success)

            # 刷新房间对象
            room.refresh_from_db()
            self.assertEqual(room.status, RoomState.IN_PROGRESS)

            # 测试无效状态转换
            success, message = await room_manager.transition_room_state(
                room.room_code, RoomState.OPEN, self.host_user
            )
            self.assertFalse(success)
            self.assertIn("无法从", message)
            return True

        result = self.run_async_test(test())
        self.assertTrue(result)
    
    async def test_leave_room_success(self):
        """测试成功离开房间"""
        # 创建房间并加入
        room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        await room_manager.join_room(room.room_code, self.participant_user)
        
        # 用户离开房间
        success, message = await room_manager.leave_room(
            room.room_code, self.participant_user
        )
        
        self.assertTrue(success)
        self.assertIn("成功", message)
        
        # 检查参与者状态
        participant = RoomParticipant.objects.get(
            room=room, user=self.participant_user
        )
        self.assertFalse(participant.is_active)
        self.assertIsNotNone(participant.left_at)
    
    async def test_host_transfer_on_leave(self):
        """测试房主离开时的权限转移"""
        # 创建房间
        room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        # 添加参与者
        await room_manager.join_room(room.room_code, self.participant_user)
        
        # 房主离开
        success, message = await room_manager.leave_room(
            room.room_code, self.host_user
        )
        
        self.assertTrue(success)
        
        # 检查房主是否转移
        room.refresh_from_db()
        self.assertEqual(room.host, self.participant_user)
        
        # 检查新房主的角色
        new_host_participant = RoomParticipant.objects.get(
            room=room, user=self.participant_user, is_active=True
        )
        self.assertEqual(new_host_participant.role, RoomParticipant.ROLE_HOST)
    
    async def test_permission_check(self):
        """测试权限检查"""
        # 创建房间
        room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        # 非房主尝试转换状态
        success, message = await room_manager.transition_room_state(
            room.room_code, RoomState.IN_PROGRESS, self.participant_user
        )
        
        self.assertFalse(success)
        self.assertIn("只有房主", message)
    
    def test_heartbeat_mechanism(self):
        """测试心跳机制"""
        room_code = "TEST01"
        
        # 更新心跳
        room_manager.update_host_heartbeat(room_code)
        
        # 检查房主是否在线
        self.assertTrue(room_manager.is_host_alive(room_code))
        
        # 模拟超时
        room_manager._host_heartbeats[room_code] = timezone.now() - timedelta(minutes=2)
        self.assertFalse(room_manager.is_host_alive(room_code))


