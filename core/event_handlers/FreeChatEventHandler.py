from typing import Dict, Any, Optional, Tuple

from events.models import EventStep
from ..models import Room
from ..consumers import database_sync_to_async
from . import BaseEventHandler

class FreeChatEventHandler(BaseEventHandler):
    """自由聊天环节处理器"""

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """启动自由聊天环节"""
        try:
            # 更新房间状态
            room.status = Room.STATUS_IN_PROGRESS
            await database_sync_to_async(room.save)()

            return {
                "room_status": room.status,
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None

        except Exception as e:
            self.logger.error(f"Error starting free chat: {e}")
            return None, "启动自由聊天时发生错误"

    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """自由聊天不需要特殊的消息处理，返回False让常规处理继续"""
        _ = user, payload  # 忽略未使用的参数
        return False

    async def handle_timeout(self) -> None:
        """处理自由聊天超时"""
        try:
            room = await self.get_room_with_template()
            if room:
                room.status = Room.STATUS_WAITING
                await database_sync_to_async(room.save)()
                await self.broadcast_to_room('broadcast_step_timeout', {
                    'room_status': room.status
                })
        except Exception as e:
            self.logger.error(f"Error handling free chat timeout: {e}")