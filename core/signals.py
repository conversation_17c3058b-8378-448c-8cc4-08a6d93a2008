"""
Core应用信号处理器

处理用户创建、房间管理等相关信号
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
import logging

logger = logging.getLogger(__name__)

User = get_user_model()


@receiver(post_save, sender=User)
def assign_default_group_to_new_user(sender, instance, created, **kwargs):
    """
    为新创建的用户自动分配默认用户组

    Args:
        sender: User模型类
        instance: 用户实例
        created: 是否是新创建的用户
        **kwargs: 其他参数
    """
    if created:
        try:
            # 检查用户是否已经有组（比如通过create_superuser创建的）
            if instance.groups.exists():
                logger.info(f"用户 {instance.username} 已有用户组，跳过默认组分配")
                return

            # 根据用户属性分配适当的组
            if getattr(instance, 'is_superuser', False):
                # 超级用户分配到超级管理员组
                admin_group, group_created = Group.objects.get_or_create(
                    name='Super Administrators'
                )
                instance.groups.add(admin_group)
                logger.info(f"用户 {instance.username} 已分配到超级管理员组: {admin_group.name}")
            elif getattr(instance, 'is_staff', False):
                # 工作人员分配到工作人员组
                staff_group, group_created = Group.objects.get_or_create(
                    name='Staff'
                )
                instance.groups.add(staff_group)
                logger.info(f"用户 {instance.username} 已分配到工作人员组: {staff_group.name}")
            else:
                # 普通用户分配到默认组
                regular_group, group_created = Group.objects.get_or_create(
                    name='Regular Users'
                )
                instance.groups.add(regular_group)
                logger.info(f"用户 {instance.username} 已分配到默认用户组: {regular_group.name}")

        except Exception as e:
            logger.error(f"为用户 {instance.username} 分配用户组时出错: {e}")


@receiver(post_save, sender=User)
def setup_user_room_limits(sender, instance, created, **kwargs):
    """
    为新用户设置房间相关的初始配置
    
    Args:
        sender: User模型类
        instance: 用户实例
        created: 是否是新创建的用户
        **kwargs: 其他参数
    """
    if created:
        # 这里可以添加其他用户初始化逻辑
        # 比如创建用户配置文件、设置默认偏好等
        logger.info(f"新用户 {instance.username} 初始化完成")
